"use client"

import { useState } from "react"

type Participant = {
  id: number
  name: string
  seed: number
  score?: number
  winner?: boolean
}

type Match = {
  id: number
  round: number
  position: number
  player1: Participant | null
  player2: Participant | null
  nextMatchId: number | null
  tournamentRoundText: string
}

const generateInitialMatches = (participants: Participant[]): Match[] => {
  // Implementasi awal bracket single elimination
  return [
    {
      id: 1,
      round: 1,
      position: 1,
      player1: participants[0],
      player2: participants[1],
      nextMatchId: 3,
      tournamentRoundText: "Round 1",
    },
    {
      id: 2,
      round: 1,
      position: 2,
      player1: participants[2],
      player2: participants[3],
      nextMatchId: 3,
      tournamentRoundText: "Round 1",
    },
    {
      id: 3,
      round: 2,
      position: 1,
      player1: null,
      player2: null,
      nextMatchId: null,
      tournamentRoundText: "Final",
    }
  ]
}

export default function TournamentBracket() {
  const [matches, setMatches] = useState<Match[]>([
    // Round 1
    {
      id: 1,
      round: 1,
      position: 1,
      player1: { id: 1, name: "<PERSON><PERSON><PERSON><PERSON>", seed: 1, score: 2, winner: false },
      player2: { id: 2, name: "<PERSON><PERSON><PERSON><PERSON>", seed: 2, score: 5, winner: true },
      nextMatchId: 5,
      tournamentRoundText: "Round 1",
    },
    {
      id: 2,
      round: 1,
      position: 2,
      player1: { id: 3, name: "Joshua", seed: 3, score: 3, winner: false },
      player2: { id: 4, name: "Prabu", seed: 4, score: 5, winner: true },
      nextMatchId: 5,
      tournamentRoundText: "Round 1",
    },
    {
      id: 3,
      round: 1,
      position: 3,
      player1: { id: 5, name: "Martin", seed: 5, score: 5, winner: true },
      player2: { id: 6, name: "Fian", seed: 6, score: 4, winner: false },
      nextMatchId: 6,
      tournamentRoundText: "Round 1",
    },
    {
      id: 4,
      round: 1,
      position: 4,
      player1: { id: 7, name: "A Wahyudin", seed: 7, score: 3, winner: false },
      player2: { id: 8, name: "HR", seed: 8, score: 5, winner: true },
      nextMatchId: 6,
      tournamentRoundText: "Round 1",
    },
    // Round 2
    {
      id: 5,
      round: 2,
      position: 1,
      player1: { id: 2, name: "M Rizaldi", seed: 8, score: 5, winner: true },
      player2: { id: 4, name: "Joshua", seed: 5, score: 3, winner: false },
      nextMatchId: 7,
      tournamentRoundText: "Round 2",
    },
    {
      id: 6,
      round: 2,
      position: 2,
      player1: { id: 5, name: "Prabu Krista", seed: 3, score: 5, winner: true },
      player2: { id: 8, name: "HR", seed: 2, score: 3, winner: false },
      nextMatchId: 7,
      tournamentRoundText: "Round 2",
    },
    // Semifinals
    {
      id: 7,
      round: 3,
      position: 1,
      player1: { id: 2, name: "M Rizaldi", seed: 8, score: 5, winner: true },
      player2: { id: 5, name: "Prabu Krista", seed: 3, score: 3, winner: false },
      nextMatchId: 8,
      tournamentRoundText: "Semifinals",
    },
    // Finals
    {
      id: 8,
      round: 4,
      position: 1,
      player1: { id: 2, name: "M Rizaldi", seed: 8, score: 5, winner: true },
      player2: { id: 8, name: "HR", seed: 2, score: 4, winner: false },
      nextMatchId: null,
      tournamentRoundText: "Finals",
    },
    // Losers Round 2
    {
      id: 9,
      round: 5,
      position: 1,
      player1: { id: 3, name: "Joshua", seed: 4, score: 5, winner: true },
      player2: { id: 9, name: "Fian", seed: 9, score: 3, winner: false },
      nextMatchId: 12,
      tournamentRoundText: "Losers Round 2",
    },
    {
      id: 10,
      round: 5,
      position: 2,
      player1: { id: 7, name: "Martin", seed: 7, score: 2, winner: false },
      player2: { id: 8, name: "HR", seed: 2, score: 5, winner: true },
      nextMatchId: 12,
      tournamentRoundText: "Losers Round 2",
    },
    // Losers Round 3
    {
      id: 12,
      round: 6,
      position: 1,
      player1: { id: 3, name: "Joshua", seed: 4, score: 2, winner: false },
      player2: { id: 8, name: "HR", seed: 2, score: 5, winner: true },
      nextMatchId: 13,
      tournamentRoundText: "Losers Round 3",
    },
    // Losers Round 4
    {
      id: 13,
      round: 7,
      position: 1,
      player1: { id: 5, name: "Prabu Krista", seed: 3, score: 3, winner: false },
      player2: { id: 8, name: "HR", seed: 2, score: 5, winner: true },
      nextMatchId: null,
      tournamentRoundText: "Losers Round 4",
    },
  ])

  const getMatchesByRound = (round: number) => {
    return matches.filter((match) => match.round === round)
  }

  const renderSeedAndName = (player: Participant | null) => {
    if (!player) return <div className="text-gray-400">TBD</div>
    return (
      <div className="flex items-center gap-2">
        <div className="w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-xs">
          {player.seed}
        </div>
        <div className="truncate">{player.name}</div>
      </div>
    )
  }

  const renderMatch = (match: Match, roundIndex: number, matchIndex: number, isLastRound: boolean = false) => {
    const showConnections = !isLastRound;
    const isEvenMatch = matchIndex % 2 === 0;

    return (
      <div key={match.id} className="flex flex-col mb-4 relative">
        {/* Connection Lines */}
        {showConnections && (
          <div className="absolute left-full top-1/2 transform -translate-y-1/2 pointer-events-none z-0">
            {/* Horizontal line going right */}
            <div
              className="absolute top-0 left-0 bracket-connector-horizontal"
              style={{
                width: '32px',
                height: '2px',
                transform: 'translateY(-1px)'
              }}
            />

            {/* Vertical connector */}
            {isEvenMatch ? (
              // Top match of pair - line goes down
              <div
                className="absolute left-8 top-0 bracket-connector-vertical"
                style={{
                  width: '2px',
                  height: '48px',
                  transform: 'translateY(-1px)'
                }}
              />
            ) : (
              // Bottom match of pair - line goes up
              <div
                className="absolute left-8 bracket-connector-vertical"
                style={{
                  width: '2px',
                  height: '48px',
                  transform: 'translateY(-47px)'
                }}
              />
            )}

            {/* Horizontal line to next match (only for top match of pair) */}
            {isEvenMatch && (
              <div
                className="absolute left-8 bracket-connector-horizontal"
                style={{
                  width: '32px',
                  height: '2px',
                  top: '47px',
                  transform: 'translateY(-1px)'
                }}
              />
            )}
          </div>
        )}

        <div className="border border-gray-700 rounded overflow-hidden relative z-10">
          {/* Match Number */}
          <div className="absolute left-2 top-1/2 transform -translate-y-1/2 text-white text-sm font-bold z-20">
            {match.id}
          </div>

          <div className={`flex items-center justify-between p-2 pl-8 ${match.player1?.winner ? "bg-[#ff7f32]" : "bg-gray-700"}`}>
            {renderSeedAndName(match.player1)}
            <div className="font-bold">{match.player1?.score || 0}</div>
          </div>
          <div className={`flex items-center justify-between p-2 pl-8 ${match.player2?.winner ? "bg-[#ff7f32]" : "bg-gray-700"}`}>
            {renderSeedAndName(match.player2)}
            <div className="font-bold">{match.player2?.score || 0}</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full overflow-x-auto">
      <div className="min-w-[800px] p-4">
        <div className="flex justify-between mb-4 border-b border-gray-700 pb-2">
          <div className="font-bold w-1/4 text-center">Round 1</div>
          <div className="font-bold w-1/4 text-center">Round 2</div>
          <div className="font-bold w-1/4 text-center">Semifinals</div>
          <div className="font-bold w-1/4 text-center">Finals</div>
        </div>

        <div className="flex">
          {/* Round 1 */}
          <div className="w-1/4 pr-4">
            {getMatchesByRound(1).map((match, index) => renderMatch(match, 1, index, false))}
          </div>

          {/* Round 2 */}
          <div className="w-1/4 pr-4">
            <div className="mt-8"></div>
            {getMatchesByRound(2).map((match, index) => renderMatch(match, 2, index, false))}
          </div>

          {/* Semifinals */}
          <div className="w-1/4 pr-4">
            <div className="mt-16"></div>
            {getMatchesByRound(3).map((match, index) => renderMatch(match, 3, index, false))}
          </div>

          {/* Finals */}
          <div className="w-1/4">
            <div className="mt-24"></div>
            {getMatchesByRound(4).map((match, index) => renderMatch(match, 4, index, true))}
          </div>
        </div>

        <div className="mt-8">
          <div className="flex justify-between mb-4 border-b border-gray-700 pb-2">
            <div className="font-bold w-1/3 text-center">Losers Round 2</div>
            <div className="font-bold w-1/3 text-center">Losers Round 3</div>
            <div className="font-bold w-1/3 text-center">Losers Round 4</div>
          </div>

          <div className="flex">
            {/* Losers Round 2 */}
            <div className="w-1/3 pr-4">
              {getMatchesByRound(5).map((match, index) => renderMatch(match, 5, index, false))}
            </div>

            {/* Losers Round 3 */}
            <div className="w-1/3 pr-4">
              <div className="mt-8"></div>
              {getMatchesByRound(6).map((match, index) => renderMatch(match, 6, index, false))}
            </div>

            {/* Losers Round 4 */}
            <div className="w-1/3">
              <div className="mt-16"></div>
              {getMatchesByRound(7).map((match, index) => renderMatch(match, 7, index, true))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import type { Match, Player } from "@/lib/tournament-engine"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface BracketVisualizerProps {
  matches: Match[]
  onUpdateMatch?: (matchId: string, winner: Player, score1: number, score2: number) => void
  readonly?: boolean
}

export default function BracketVisualizer({ matches, onUpdateMatch, readonly = false }: BracketVisualizerProps) {
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null)
  const [score1, setScore1] = useState("")
  const [score2, setScore2] = useState("")

  const getMatchesByRound = (round: number) => {
    return matches.filter((match) => match.round === round && !match.isLosersBracket)
  }

  const getLosersMatchesByRound = (round: number) => {
    return matches.filter((match) => match.round === round && match.isLosersBracket)
  }

  const maxRound = Math.max(...matches.map((m) => m.round))
  const maxLosersRound = Math.max(...matches.filter((m) => m.isLosersBracket).map((m) => m.round), 0)

  const handleUpdateMatch = () => {
    if (!selectedMatch || !onUpdateMatch) return

    const winner = Number.parseInt(score1) > Number.parseInt(score2) ? selectedMatch.player1 : selectedMatch.player2
    if (winner) {
      onUpdateMatch(selectedMatch.id, winner, Number.parseInt(score1), Number.parseInt(score2))
      setSelectedMatch(null)
      setScore1("")
      setScore2("")
    }
  }

  const renderMatch = (match: Match) => {
    const isCompleted = match.status === "completed"

    return (
      <div key={match.id} className="mb-4">
        <div
          className={`border border-gray-700 rounded overflow-hidden cursor-pointer hover:border-gray-500 ${!readonly ? "hover:bg-gray-800" : ""}`}
          onClick={() => !readonly && setSelectedMatch(match)}
        >
          <div
            className={`flex items-center justify-between p-2 ${
              isCompleted && match.winner?.id === match.player1?.id ? "bg-[#ff7f32]" : "bg-gray-700"
            }`}
          >
            <div className="flex items-center gap-2">
              {match.player1?.seed && (
                <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-xs">
                  {match.player1.seed}
                </div>
              )}
              <div className="truncate">{match.player1?.name || "TBD"}</div>
            </div>
            <div className="font-bold">{isCompleted ? match.score1 : "-"}</div>
          </div>
          <div
            className={`flex items-center justify-between p-2 ${
              isCompleted && match.winner?.id === match.player2?.id ? "bg-[#ff7f32]" : "bg-gray-700"
            }`}
          >
            <div className="flex items-center gap-2">
              {match.player2?.seed && (
                <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-xs">
                  {match.player2.seed}
                </div>
              )}
              <div className="truncate">{match.player2?.name || "TBD"}</div>
            </div>
            <div className="font-bold">{isCompleted ? match.score2 : "-"}</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full overflow-x-auto">
      <div className="min-w-[800px] p-4">
        {/* Winners Bracket */}
        <div className="mb-8">
          <h3 className="text-lg font-bold mb-4">Winners Bracket</h3>
          <div className="flex justify-between mb-4 border-b border-gray-700 pb-2">
            {Array.from({ length: maxRound }, (_, i) => (
              <div key={i} className="font-bold w-1/4 text-center">
                {i === maxRound - 1 ? "Finals" : i === maxRound - 2 ? "Semifinals" : `Round ${i + 1}`}
              </div>
            ))}
          </div>

          <div className="flex">
            {Array.from({ length: maxRound }, (_, round) => (
              <div key={round} className="w-1/4 pr-4">
                <div style={{ marginTop: `${round * 32}px` }}>
                  {getMatchesByRound(round + 1).map((match) => renderMatch(match))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Losers Bracket */}
        {maxLosersRound > 0 && (
          <div>
            <h3 className="text-lg font-bold mb-4">Losers Bracket</h3>
            <div className="flex justify-between mb-4 border-b border-gray-700 pb-2">
              {Array.from({ length: maxLosersRound - 100 }, (_, i) => (
                <div key={i} className="font-bold text-center" style={{ width: `${100 / (maxLosersRound - 100)}%` }}>
                  Losers Round {i + 1}
                </div>
              ))}
            </div>

            <div className="flex">
              {Array.from({ length: maxLosersRound - 100 }, (_, round) => (
                <div key={round} style={{ width: `${100 / (maxLosersRound - 100)}%` }} className="pr-4">
                  <div style={{ marginTop: `${round * 16}px` }}>
                    {getLosersMatchesByRound(round + 101).map((match) => renderMatch(match))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Match Update Dialog */}
      {selectedMatch && !readonly && (
        <Dialog open={!!selectedMatch} onOpenChange={() => setSelectedMatch(null)}>
          <DialogContent className="bg-[#2a2a2a] border-gray-700 text-white">
            <DialogHeader>
              <DialogTitle>Update Match Result</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>{selectedMatch.player1?.name || "TBD"}</span>
                <Input
                  type="number"
                  value={score1}
                  onChange={(e) => setScore1(e.target.value)}
                  className="w-20 bg-[#333333] border-gray-700 text-white"
                  placeholder="0"
                />
              </div>
              <div className="flex items-center justify-between">
                <span>{selectedMatch.player2?.name || "TBD"}</span>
                <Input
                  type="number"
                  value={score2}
                  onChange={(e) => setScore2(e.target.value)}
                  className="w-20 bg-[#333333] border-gray-700 text-white"
                  placeholder="0"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setSelectedMatch(null)}>
                  Cancel
                </Button>
                <Button
                  onClick={handleUpdateMatch}
                  className="bg-[#ff7f32] hover:bg-[#e86a1f]"
                  disabled={!score1 || !score2}
                >
                  Update
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

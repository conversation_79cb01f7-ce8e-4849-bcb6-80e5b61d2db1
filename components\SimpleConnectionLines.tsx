'use client';

import React from 'react';

interface SimpleConnectionLinesProps {
  show: boolean;
  matchPosition: number;
  isLastRound?: boolean;
}

export const SimpleConnectionLines: React.FC<SimpleConnectionLinesProps> = ({
  show,
  matchPosition,
  isLastRound = false
}) => {
  if (!show || isLastRound) return null;
  
  const isEvenMatch = matchPosition % 2 === 0;
  
  return (
    <div className="absolute left-full top-1/2 transform -translate-y-1/2 pointer-events-none z-0">
      {/* Horizontal line from match */}
      <div 
        className="absolute top-0 left-0 bg-gray-500"
        style={{
          width: '50px',
          height: '2px',
          transform: 'translateY(-1px)'
        }}
      />
      
      {/* Vertical connector line */}
      {isEvenMatch ? (
        // Top match - line goes down
        <div 
          className="absolute left-12 top-0 bg-gray-500"
          style={{
            width: '2px',
            height: '75px',
            transform: 'translateY(-1px)'
          }}
        />
      ) : (
        // Bottom match - line goes up
        <div 
          className="absolute left-12 bg-gray-500"
          style={{
            width: '2px',
            height: '75px',
            transform: 'translateY(-74px)'
          }}
        />
      )}
      
      {/* Horizontal line to next match (only for top match of pair) */}
      {isEvenMatch && (
        <div 
          className="absolute left-12 bg-gray-500"
          style={{
            width: '50px',
            height: '2px',
            top: '74px',
            transform: 'translateY(-1px)'
          }}
        />
      )}
    </div>
  );
};

export default SimpleConnectionLines;

"use client";

import React from "react";
import { useActiveMenu, DynamicBreadcrumb } from "@/components/DynamicSidebar";
import { cn } from "@/lib/utils";
import { 
  LayoutDashboard, 
  Plus, 
  Trophy, 
  User,
  Settings,
  Calendar,
  Users,
  Award,
  TrendingUp
} from "lucide-react";

interface DynamicPageHeaderProps {
  title?: string;
  subtitle?: string;
  showBreadcrumb?: boolean;
  showStats?: boolean;
  children?: React.ReactNode;
  className?: string;
}

export default function DynamicPageHeader({
  title,
  subtitle,
  showBreadcrumb = true,
  showStats = false,
  children,
  className
}: DynamicPageHeaderProps) {
  const { activeMenuItem } = useActiveMenu();

  // Get dynamic title and subtitle based on active menu
  const dynamicTitle = title || activeMenuItem?.label || "Dashboard";
  const dynamicSubtitle = subtitle || activeMenuItem?.description || "Welcome to PORDI Tournament Management";

  // Get icon for current page
  const getPageIcon = () => {
    if (!activeMenuItem) return LayoutDashboard;
    return activeMenuItem.icon;
  };

  const PageIcon = getPageIcon();

  return (
    <div className={cn(
      "bg-gradient-to-r from-[#2a2a2a] to-[#333333] border-b border-gray-700",
      "px-6 py-8 mb-8",
      className
    )}>
      <div className="max-w-7xl mx-auto">
        {/* Breadcrumb */}
        {showBreadcrumb && (
          <div className="mb-6">
            <DynamicBreadcrumb />
          </div>
        )}

        {/* Header Content */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          {/* Title Section */}
          <div>
            {/* Title & Subtitle */}
            <div>
              <h1 className="text-3xl font-bold text-white mb-1">
                {dynamicTitle}
              </h1>
              <p className="text-gray-400 text-lg">
                {dynamicSubtitle}
              </p>
            </div>
          </div>

          {/* Action Section */}
          {children && (
            <div className="flex items-center gap-4">
              {children}
            </div>
          )}
        </div>

        {/* Stats Section */}
        {showStats && (
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <StatCard
              icon={Trophy}
              label="Active Tournaments"
              value="12"
              trend="+3"
              trendUp={true}
            />
            <StatCard
              icon={Users}
              label="Total Participants"
              value="1,247"
              trend="+156"
              trendUp={true}
            />
            <StatCard
              icon={Award}
              label="Completed Events"
              value="89"
              trend="+8"
              trendUp={true}
            />
            <StatCard
              icon={Calendar}
              label="This Month"
              value="24"
              trend="+12"
              trendUp={true}
            />
          </div>
        )}
      </div>
    </div>
  );
}

// Stat Card Component
interface StatCardProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  value: string;
  trend?: string;
  trendUp?: boolean;
}

function StatCard({ icon: Icon, label, value, trend, trendUp }: StatCardProps) {
  return (
    <div className="bg-[#1a1a1a] rounded-lg p-4 border border-gray-700 hover:border-[#ff7f32]/30 transition-all duration-200">
      <div className="flex items-center justify-between mb-2">
        <Icon className="w-5 h-5 text-[#ff7f32]" />
        {trend && (
          <div className={cn(
            "flex items-center gap-1 text-xs font-semibold",
            trendUp ? "text-green-400" : "text-red-400"
          )}>
            <TrendingUp className={cn(
              "w-3 h-3",
              !trendUp && "rotate-180"
            )} />
            {trend}
          </div>
        )}
      </div>
      <div className="text-2xl font-bold text-white mb-1">
        {value}
      </div>
      <div className="text-sm text-gray-400">
        {label}
      </div>
    </div>
  );
}

// Quick Action Button Component
interface QuickActionButtonProps {
  href?: string;
  onClick?: () => void;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  variant?: "primary" | "secondary" | "outline";
  className?: string;
}

export function QuickActionButton({
  href,
  onClick,
  icon: Icon,
  label,
  variant = "primary",
  className
}: QuickActionButtonProps) {
  const baseStyles = "flex items-center gap-2 px-4 py-2 rounded-lg font-semibold transition-all duration-200 hover:scale-105";
  
  const variantStyles = {
    primary: "bg-gradient-to-r from-[#ff7f32] to-[#e86a1f] text-white shadow-lg hover:shadow-xl",
    secondary: "bg-[#333333] text-white hover:bg-[#404040] border border-gray-600",
    outline: "border-2 border-[#ff7f32] text-[#ff7f32] hover:bg-[#ff7f32] hover:text-white"
  };

  const buttonClass = cn(baseStyles, variantStyles[variant], className);

  const content = (
    <>
      <Icon className="w-4 h-4" />
      {label}
    </>
  );

  if (href) {
    return (
      <a href={href} className={buttonClass}>
        {content}
      </a>
    );
  }

  return (
    <button onClick={onClick} className={buttonClass}>
      {content}
    </button>
  );
}

// Page Status Indicator
interface PageStatusProps {
  status: "online" | "maintenance" | "error";
  message?: string;
}

export function PageStatus({ status, message }: PageStatusProps) {
  const statusConfig = {
    online: {
      color: "text-green-400",
      bgColor: "bg-green-400/10",
      borderColor: "border-green-400/30",
      icon: "●",
      defaultMessage: "System Online"
    },
    maintenance: {
      color: "text-yellow-400",
      bgColor: "bg-yellow-400/10",
      borderColor: "border-yellow-400/30",
      icon: "⚠",
      defaultMessage: "System maintenance in progress"
    },
    error: {
      color: "text-red-400",
      bgColor: "bg-red-400/10",
      borderColor: "border-red-400/30",
      icon: "✕",
      defaultMessage: "System error detected"
    }
  };

  const config = statusConfig[status];

  return (
    <div className={cn(
      "flex items-center gap-2 px-3 py-1 rounded-full border text-sm",
      config.bgColor,
      config.borderColor,
      config.color
    )}>
      <span className="animate-pulse">{config.icon}</span>
      <span className="font-medium">
        {message || config.defaultMessage}
      </span>
    </div>
  );
}

// Export all components
export {
  StatCard
};

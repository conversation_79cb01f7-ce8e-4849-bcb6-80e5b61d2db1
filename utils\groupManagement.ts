/**
 * Group Management System for SE/DE Tournaments
 * Handles group creation, participant distribution, and results tracking
 * Created by: Panglima's Tournament Management System
 */

export interface Participant {
  noReg: string;
  name: string;
  club: string;
}

export interface GroupResult {
  participant: Participant;
  rank: number;
  points: number;
  wins: number;
  losses: number;
  eliminated: boolean;
}

export interface Group {
  groupName: string;
  participants: Participant[];
  format: 'single' | 'double'; // SE or DE
  status: 'pending' | 'ongoing' | 'completed';
  results: GroupResult[];
  advanceCount: number; // How many advance to next stage
}

export interface TournamentGroup {
  tournamentId: string;
  groups: Group[];
  stage: 'stage1' | 'final';
  totalParticipants: number;
  advancePerGroup: number;
}

export class GroupManager {
  private tournamentId: string;
  private groups: Group[] = [];

  constructor(tournamentId: string) {
    this.tournamentId = tournamentId;
    this.loadGroups();
  }

  /**
   * Create groups for SE/DE tournament
   */
  createSEDEGroups(
    participants: Participant[], 
    format: 'single' | 'double',
    advancePerGroup: number = 2
  ): Group[] {
    // Validate participant count
    if (participants.length < 8) {
      throw new Error('Minimum 8 participants required for SE/DE groups');
    }

    // Calculate optimal group distribution (8-32 participants per group)
    const groupDistribution = this.calculateGroupDistribution(participants.length);
    
    if (!groupDistribution.valid) {
      throw new Error('Cannot create valid group distribution for SE/DE format');
    }

    // Distribute participants into groups
    const groups: Group[] = [];
    let participantIndex = 0;

    groupDistribution.sizes.forEach((size, index) => {
      const groupParticipants = participants.slice(participantIndex, participantIndex + size);
      
      const group: Group = {
        groupName: `Group ${String(index + 1).padStart(4, '0')}`,
        participants: groupParticipants,
        format,
        status: 'pending',
        results: [],
        advanceCount: Math.min(advancePerGroup, Math.floor(size / 2)) // Max half can advance
      };

      groups.push(group);
      participantIndex += size;
    });

    this.groups = groups;
    this.saveGroups();
    return groups;
  }

  /**
   * Calculate optimal group distribution for SE/DE
   */
  private calculateGroupDistribution(totalParticipants: number): {
    valid: boolean;
    sizes: number[];
    description: string;
  } {
    if (totalParticipants < 8) {
      return { valid: false, sizes: [], description: 'Minimum 8 participants required' };
    }

    // Valid group sizes: 8, 9, 16, 17, 32, 33 (powers of 2 and powers of 2 + 1)
    const validSizes = [8, 9, 16, 17, 32, 33];
    
    // Try to find optimal distribution
    const result = this.findOptimalDistribution(totalParticipants, validSizes);
    
    if (result.valid) {
      return {
        valid: true,
        sizes: result.sizes,
        description: this.generateDistributionDescription(result.sizes)
      };
    }

    return { valid: false, sizes: [], description: 'Cannot create valid distribution' };
  }

  /**
   * Find optimal distribution using dynamic programming approach
   */
  private findOptimalDistribution(total: number, validSizes: number[]): {
    valid: boolean;
    sizes: number[];
  } {
    // Sort sizes in descending order for better distribution
    const sortedSizes = [...validSizes].sort((a, b) => b - a);
    
    // Try different combinations
    for (let maxGroups = 1; maxGroups <= Math.ceil(total / 8); maxGroups++) {
      const result = this.tryDistribution(total, sortedSizes, maxGroups);
      if (result.valid) {
        return result;
      }
    }

    return { valid: false, sizes: [] };
  }

  /**
   * Try specific distribution with given constraints
   */
  private tryDistribution(total: number, sizes: number[], maxGroups: number): {
    valid: boolean;
    sizes: number[];
  } {
    const dp: { [key: string]: number[] | null } = {};
    
    const solve = (remaining: number, groupsUsed: number): number[] | null => {
      if (remaining === 0) return [];
      if (groupsUsed >= maxGroups) return null;
      
      const key = `${remaining}-${groupsUsed}`;
      if (key in dp) return dp[key];
      
      for (const size of sizes) {
        if (size <= remaining) {
          const subResult = solve(remaining - size, groupsUsed + 1);
          if (subResult !== null) {
            const result = [size, ...subResult];
            dp[key] = result;
            return result;
          }
        }
      }
      
      dp[key] = null;
      return null;
    };

    const result = solve(total, 0);
    return {
      valid: result !== null,
      sizes: result || []
    };
  }

  /**
   * Generate human-readable distribution description
   */
  private generateDistributionDescription(sizes: number[]): string {
    const counts: { [size: number]: number } = {};
    sizes.forEach(size => {
      counts[size] = (counts[size] || 0) + 1;
    });

    const parts: string[] = [];
    Object.entries(counts)
      .sort(([a], [b]) => parseInt(b) - parseInt(a))
      .forEach(([size, count]) => {
        parts.push(`${count} group${count > 1 ? 's' : ''} with ${size} participants`);
      });

    return parts.join(', ');
  }

  /**
   * Update group results from bracket matches
   */
  updateGroupResults(groupIndex: number, results: GroupResult[]): void {
    if (groupIndex >= 0 && groupIndex < this.groups.length) {
      this.groups[groupIndex].results = results;
      this.groups[groupIndex].status = 'completed';
      this.saveGroups();
    }
  }

  /**
   * Get winners from all groups for next stage
   */
  getGroupWinners(): GroupResult[] {
    const winners: GroupResult[] = [];
    
    this.groups.forEach(group => {
      if (group.results.length > 0) {
        // Sort by rank and take top advanceCount
        const sortedResults = [...group.results].sort((a, b) => a.rank - b.rank);
        const groupWinners = sortedResults.slice(0, group.advanceCount);
        winners.push(...groupWinners);
      }
    });

    return winners;
  }

  /**
   * Check if all groups are completed
   */
  areAllGroupsCompleted(): boolean {
    return this.groups.every(group => group.status === 'completed');
  }

  /**
   * Get group statistics
   */
  getGroupStatistics(): {
    totalGroups: number;
    completedGroups: number;
    totalParticipants: number;
    totalWinners: number;
    averageGroupSize: number;
  } {
    const totalGroups = this.groups.length;
    const completedGroups = this.groups.filter(g => g.status === 'completed').length;
    const totalParticipants = this.groups.reduce((sum, g) => sum + g.participants.length, 0);
    const totalWinners = this.groups.reduce((sum, g) => sum + g.advanceCount, 0);
    const averageGroupSize = totalGroups > 0 ? totalParticipants / totalGroups : 0;

    return {
      totalGroups,
      completedGroups,
      totalParticipants,
      totalWinners,
      averageGroupSize
    };
  }

  /**
   * Save groups to localStorage
   */
  private saveGroups(): void {
    const tournamentGroup: TournamentGroup = {
      tournamentId: this.tournamentId,
      groups: this.groups,
      stage: 'stage1',
      totalParticipants: this.groups.reduce((sum, g) => sum + g.participants.length, 0),
      advancePerGroup: this.groups[0]?.advanceCount || 2
    };

    localStorage.setItem(`tournament_groups_${this.tournamentId}`, JSON.stringify(tournamentGroup));
    
    // Also save in legacy format for compatibility
    const legacyGroups = this.groups.map(g => g.participants);
    localStorage.setItem('tournamentGroups', JSON.stringify(legacyGroups));
  }

  /**
   * Load groups from localStorage
   */
  private loadGroups(): void {
    try {
      const data = localStorage.getItem(`tournament_groups_${this.tournamentId}`);
      if (data) {
        const tournamentGroup: TournamentGroup = JSON.parse(data);
        this.groups = tournamentGroup.groups;
      } else {
        // Try legacy format
        const legacyData = localStorage.getItem('tournamentGroups');
        if (legacyData) {
          const legacyGroups = JSON.parse(legacyData);
          this.groups = legacyGroups.map((participants: Participant[], index: number) => ({
            groupName: `Group ${String(index + 1).padStart(4, '0')}`,
            participants,
            format: 'single' as const,
            status: 'pending' as const,
            results: [],
            advanceCount: 2
          }));
        }
      }
    } catch (error) {
      console.error('Error loading groups:', error);
      this.groups = [];
    }
  }

  /**
   * Get all groups
   */
  getGroups(): Group[] {
    return this.groups;
  }

  /**
   * Get specific group
   */
  getGroup(index: number): Group | null {
    return this.groups[index] || null;
  }

  /**
   * Reset all groups
   */
  resetGroups(): void {
    this.groups = [];
    localStorage.removeItem(`tournament_groups_${this.tournamentId}`);
  }
}

/**
 * Utility functions for group management
 */
export const GroupUtils = {
  /**
   * Validate group size for SE/DE format
   */
  isValidGroupSize: (size: number): boolean => {
    const validSizes = [8, 9, 16, 17, 32, 33];
    return validSizes.includes(size);
  },

  /**
   * Calculate minimum groups needed
   */
  calculateMinGroups: (totalParticipants: number): number => {
    return Math.ceil(totalParticipants / 32); // Max 32 per group
  },

  /**
   * Calculate maximum groups possible
   */
  calculateMaxGroups: (totalParticipants: number): number => {
    return Math.floor(totalParticipants / 8); // Min 8 per group
  },

  /**
   * Generate group names
   */
  generateGroupNames: (count: number): string[] => {
    return Array.from({ length: count }, (_, i) => 
      `Group ${String(i + 1).padStart(4, '0')}`
    );
  },

  /**
   * Validate advance count
   */
  validateAdvanceCount: (groupSize: number, advanceCount: number): boolean => {
    return advanceCount >= 1 && advanceCount <= Math.floor(groupSize / 2);
  }
};

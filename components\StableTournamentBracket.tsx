'use client';

import React, { useState } from 'react';

interface Participant {
  name: string;
  regNumber: string;
  score?: number;
}

interface Match {
  id: number;
  participant1: Participant | null;
  participant2: Participant | null;
}

interface StableTournamentBracketProps {
  participants: string[];
  onMatchUpdate?: (matchId: string, score1: number, score2: number) => void;
}

const StableTournamentBracket: React.FC<StableTournamentBracketProps> = ({
  participants,
  onMatchUpdate
}) => {
  const [matchScores, setMatchScores] = useState<{[key: string]: {score1: number, score2: number}}>({});

  // Parse participants from format "0001 ] Player Name"
  const parseParticipant = (participantStr: string): Participant => {
    if (!participantStr || participantStr === 'TBD') {
      return { name: 'TBD', regNumber: '000' };
    }
    
    const match = participantStr.match(/^(\d+)\s*\]\s*(.+)$/);
    if (match) {
      return {
        name: match[2].trim(),
        regNumber: match[1]
      };
    }
    
    return { name: participantStr, regNumber: '000' };
  };

  // Generate matches
  const generateMatches = () => {
    const parsedParticipants = participants.map(parseParticipant);
    
    // Ensure we have 8 participants
    while (parsedParticipants.length < 8) {
      parsedParticipants.push({ name: 'TBD', regNumber: '000' });
    }

    return {
      quarterFinals: [
        { id: 1, participant1: parsedParticipants[0], participant2: parsedParticipants[1] },
        { id: 2, participant1: parsedParticipants[2], participant2: parsedParticipants[3] },
        { id: 3, participant1: parsedParticipants[4], participant2: parsedParticipants[5] },
        { id: 4, participant1: parsedParticipants[6], participant2: parsedParticipants[7] }
      ],
      semiFinals: [
        { id: 5, participant1: { name: 'Winner QF1', regNumber: '000' }, participant2: { name: 'Winner QF2', regNumber: '000' } },
        { id: 6, participant1: { name: 'Winner QF3', regNumber: '000' }, participant2: { name: 'Winner QF4', regNumber: '000' } }
      ],
      final: [
        { id: 7, participant1: { name: 'Winner SF1', regNumber: '000' }, participant2: { name: 'Winner SF2', regNumber: '000' } }
      ]
    };
  };

  const matches = generateMatches();

  const handleScoreUpdate = (matchId: number, participantIndex: number, score: number) => {
    const key = `match-${matchId}`;
    const currentScores = matchScores[key] || { score1: 0, score2: 0 };
    
    if (participantIndex === 0) {
      currentScores.score1 = score;
    } else {
      currentScores.score2 = score;
    }
    
    setMatchScores(prev => ({
      ...prev,
      [key]: currentScores
    }));

    if (onMatchUpdate) {
      onMatchUpdate(key, currentScores.score1, currentScores.score2);
    }
  };

  const renderParticipant = (participant: Participant | null, matchId: number, participantIndex: number) => {
    if (!participant) {
      return (
        <div className="flex items-center justify-between p-3 bg-[#404040] text-gray-400 border-b border-gray-600 last:border-b-0">
          <span className="text-sm">TBD</span>
          <span className="font-mono text-xs">-</span>
        </div>
      );
    }

    const matchKey = `match-${matchId}`;
    const scores = matchScores[matchKey] || { score1: 0, score2: 0 };
    const score = participantIndex === 0 ? scores.score1 : scores.score2;

    return (
      <div className="flex items-center justify-between p-3 bg-[#404040] text-white border-b border-gray-600 last:border-b-0 hover:bg-[#4a4a4a] transition-colors">
        <span className="text-sm flex-1">
          <span className="font-mono text-xs text-gray-300 mr-2">{participant.regNumber} ]</span>
          <span className="font-medium">{participant.name}</span>
        </span>
        <input 
          type="number" 
          className="w-12 h-8 ml-2 rounded bg-[#333] text-center text-sm border border-gray-600 text-white font-bold" 
          min={0}
          value={score || ''}
          onChange={(e) => handleScoreUpdate(matchId, participantIndex, parseInt(e.target.value) || 0)}
          disabled={participant.name === 'TBD' || participant.name.startsWith('Winner')}
        />
      </div>
    );
  };

  const renderMatch = (match: Match, showConnections: boolean = false) => {
    return (
      <div key={match.id} className="relative mb-6">
        {/* Connection Lines */}
        {showConnections && (
          <div className="absolute left-full top-1/2 transform -translate-y-1/2 pointer-events-none z-0">
            <div className="w-8 h-0.5 bg-gray-400"></div>
          </div>
        )}
        
        {/* Match Container */}
        <div className="border border-gray-600 rounded-lg overflow-hidden bg-[#333333] relative z-10 w-60 shadow-lg">
          {/* Match ID */}
          <div className="absolute left-3 top-2 text-white text-sm font-bold z-20 bg-[#ff7f32] px-2 py-1 rounded text-xs">
            {match.id}
          </div>
          
          {/* Participants */}
          <div className="pt-8">
            {renderParticipant(match.participant1, match.id, 0)}
            {renderParticipant(match.participant2, match.id, 1)}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full overflow-x-auto bg-[#333333] p-6 rounded-lg">
      <div className="min-w-[900px]">
        {/* Headers */}
        <div className="flex justify-between mb-6 border-b border-gray-700 pb-4">
          <div className="w-1/3 text-center">
            <h3 className="font-bold text-[#ff7f32] text-lg">Quarter Finals</h3>
          </div>
          <div className="w-1/3 text-center">
            <h3 className="font-bold text-[#ff7f32] text-lg">Semi Finals</h3>
          </div>
          <div className="w-1/3 text-center">
            <h3 className="font-bold text-[#ff7f32] text-lg">Final</h3>
          </div>
        </div>

        {/* Bracket Layout */}
        <div className="flex justify-between items-start">
          {/* Quarter Finals */}
          <div className="w-1/3 pr-8">
            {matches.quarterFinals.map((match) => renderMatch(match, true))}
          </div>

          {/* Semi Finals */}
          <div className="w-1/3 pr-8 pt-16">
            {matches.semiFinals.map((match) => renderMatch(match, true))}
          </div>

          {/* Final */}
          <div className="w-1/3 pt-32">
            {matches.final.map((match) => renderMatch(match, false))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StableTournamentBracket;

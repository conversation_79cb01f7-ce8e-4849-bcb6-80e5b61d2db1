/**
 * Tournament Bracket Configuration System
 * Comprehensive customization options for dynamic brackets
 * Created by: Panglima's Dynamic Bracket System
 */

export interface BracketTheme {
  name: string;
  colors: {
    background: string;
    matchBackground: string;
    matchBorder: string;
    matchHover: string;
    text: string;
    textSecondary: string;
    accent: string;
    winner: string;
    loser: string;
    connection: string;
  };
  gradients: {
    background: string;
    match: string;
    matchNumber: string;
  };
}

export interface BracketSpacing {
  name: string;
  roundGap: number;
  verticalGap: number;
  matchPadding: number;
  participantPadding: number;
  connectionWidth: number;
}

export interface BracketSizing {
  name: string;
  matchHeight: number;
  matchMinWidth: number;
  fontSize: {
    participantName: number;
    matchNumber: number;
    roundHeader: number;
  };
  scoreInputSize: {
    width: number;
    height: number;
  };
}

export interface BracketAnimation {
  enabled: boolean;
  duration: number;
  easing: string;
  staggerDelay: number;
}

export interface BracketConfiguration {
  // Core Settings
  participants: number;
  seedingMethod: 'random' | 'seeded' | 'manual';
  eliminationType: 'single' | 'double';
  
  // Layout Settings
  centerAlignment: boolean;
  showConnections: boolean;
  showMatchNumbers: boolean;
  showRoundHeaders: boolean;
  
  // Responsive Settings
  responsive: boolean;
  breakpoints: {
    mobile: number;
    tablet: number;
    desktop: number;
    wide: number;
  };
  
  // Visual Settings
  theme: BracketTheme;
  spacing: BracketSpacing;
  sizing: BracketSizing;
  animation: BracketAnimation;
  
  // Functional Settings
  allowScoreInput: boolean;
  autoAdvanceWinners: boolean;
  highlightWinners: boolean;
  showByes: boolean;
  
  // Export Settings
  exportFormat: 'pdf' | 'png' | 'svg';
  printOptimized: boolean;
}

// Predefined Themes
export const BRACKET_THEMES: Record<string, BracketTheme> = {
  dark: {
    name: 'Dark Professional',
    colors: {
      background: '#1a1a1a',
      matchBackground: '#404040',
      matchBorder: '#666666',
      matchHover: '#ff7f32',
      text: '#ffffff',
      textSecondary: '#cccccc',
      accent: '#ff7f32',
      winner: '#22c55e',
      loser: '#ef4444',
      connection: '#999999'
    },
    gradients: {
      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)',
      match: 'linear-gradient(135deg, #404040 0%, #353535 100%)',
      matchNumber: 'linear-gradient(135deg, #ff7f32, #ff9f52)'
    }
  },
  
  light: {
    name: 'Light Clean',
    colors: {
      background: '#f8fafc',
      matchBackground: '#ffffff',
      matchBorder: '#e2e8f0',
      matchHover: '#3b82f6',
      text: '#1e293b',
      textSecondary: '#64748b',
      accent: '#3b82f6',
      winner: '#059669',
      loser: '#dc2626',
      connection: '#94a3b8'
    },
    gradients: {
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
      match: 'linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%)',
      matchNumber: 'linear-gradient(135deg, #3b82f6, #60a5fa)'
    }
  },
  
  blue: {
    name: 'Ocean Blue',
    colors: {
      background: '#0f172a',
      matchBackground: '#1e3a8a',
      matchBorder: '#3b82f6',
      matchHover: '#60a5fa',
      text: '#ffffff',
      textSecondary: '#cbd5e1',
      accent: '#60a5fa',
      winner: '#10b981',
      loser: '#f59e0b',
      connection: '#64748b'
    },
    gradients: {
      background: 'linear-gradient(135deg, #0f172a 0%, #1e3a8a 100%)',
      match: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)',
      matchNumber: 'linear-gradient(135deg, #60a5fa, #93c5fd)'
    }
  },
  
  green: {
    name: 'Forest Green',
    colors: {
      background: '#064e3b',
      matchBackground: '#166534',
      matchBorder: '#22c55e',
      matchHover: '#4ade80',
      text: '#ffffff',
      textSecondary: '#d1fae5',
      accent: '#4ade80',
      winner: '#fbbf24',
      loser: '#ef4444',
      connection: '#6b7280'
    },
    gradients: {
      background: 'linear-gradient(135deg, #064e3b 0%, #166534 100%)',
      match: 'linear-gradient(135deg, #166534 0%, #22c55e 100%)',
      matchNumber: 'linear-gradient(135deg, #4ade80, #86efac)'
    }
  }
};

// Predefined Spacing Options
export const BRACKET_SPACING: Record<string, BracketSpacing> = {
  compact: {
    name: 'Compact',
    roundGap: 120,
    verticalGap: 8,
    matchPadding: 8,
    participantPadding: 8,
    connectionWidth: 24
  },
  
  normal: {
    name: 'Normal',
    roundGap: 160,
    verticalGap: 16,
    matchPadding: 12,
    participantPadding: 12,
    connectionWidth: 32
  },
  
  comfortable: {
    name: 'Comfortable',
    roundGap: 200,
    verticalGap: 24,
    matchPadding: 16,
    participantPadding: 16,
    connectionWidth: 40
  },
  
  spacious: {
    name: 'Spacious',
    roundGap: 240,
    verticalGap: 32,
    matchPadding: 20,
    participantPadding: 20,
    connectionWidth: 48
  }
};

// Predefined Sizing Options
export const BRACKET_SIZING: Record<string, BracketSizing> = {
  small: {
    name: 'Small',
    matchHeight: 60,
    matchMinWidth: 140,
    fontSize: {
      participantName: 11,
      matchNumber: 10,
      roundHeader: 12
    },
    scoreInputSize: {
      width: 32,
      height: 24
    }
  },
  
  medium: {
    name: 'Medium',
    matchHeight: 80,
    matchMinWidth: 180,
    fontSize: {
      participantName: 13,
      matchNumber: 11,
      roundHeader: 14
    },
    scoreInputSize: {
      width: 40,
      height: 28
    }
  },
  
  large: {
    name: 'Large',
    matchHeight: 100,
    matchMinWidth: 220,
    fontSize: {
      participantName: 15,
      matchNumber: 12,
      roundHeader: 16
    },
    scoreInputSize: {
      width: 48,
      height: 32
    }
  },
  
  extraLarge: {
    name: 'Extra Large',
    matchHeight: 120,
    matchMinWidth: 260,
    fontSize: {
      participantName: 17,
      matchNumber: 14,
      roundHeader: 18
    },
    scoreInputSize: {
      width: 56,
      height: 36
    }
  }
};

// Default Configuration
export const DEFAULT_BRACKET_CONFIG: BracketConfiguration = {
  // Core Settings
  participants: 8,
  seedingMethod: 'random',
  eliminationType: 'single',
  
  // Layout Settings
  centerAlignment: true,
  showConnections: true,
  showMatchNumbers: true,
  showRoundHeaders: true,
  
  // Responsive Settings
  responsive: true,
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
    wide: 1536
  },
  
  // Visual Settings
  theme: BRACKET_THEMES.dark,
  spacing: BRACKET_SPACING.normal,
  sizing: BRACKET_SIZING.medium,
  animation: {
    enabled: true,
    duration: 500,
    easing: 'ease-out',
    staggerDelay: 100
  },
  
  // Functional Settings
  allowScoreInput: true,
  autoAdvanceWinners: true,
  highlightWinners: true,
  showByes: false,
  
  // Export Settings
  exportFormat: 'pdf',
  printOptimized: true
};

// Configuration Builder Class
export class BracketConfigurationBuilder {
  private config: BracketConfiguration;

  constructor(baseConfig: Partial<BracketConfiguration> = {}) {
    this.config = { ...DEFAULT_BRACKET_CONFIG, ...baseConfig };
  }

  // Core Settings
  setParticipants(count: number): this {
    this.config.participants = count;
    return this;
  }

  setSeedingMethod(method: 'random' | 'seeded' | 'manual'): this {
    this.config.seedingMethod = method;
    return this;
  }

  setEliminationType(type: 'single' | 'double'): this {
    this.config.eliminationType = type;
    return this;
  }

  // Visual Settings
  setTheme(themeName: keyof typeof BRACKET_THEMES): this {
    this.config.theme = BRACKET_THEMES[themeName];
    return this;
  }

  setSpacing(spacingName: keyof typeof BRACKET_SPACING): this {
    this.config.spacing = BRACKET_SPACING[spacingName];
    return this;
  }

  setSizing(sizingName: keyof typeof BRACKET_SIZING): this {
    this.config.sizing = BRACKET_SIZING[sizingName];
    return this;
  }

  // Layout Settings
  setCenterAlignment(enabled: boolean): this {
    this.config.centerAlignment = enabled;
    return this;
  }

  setShowConnections(enabled: boolean): this {
    this.config.showConnections = enabled;
    return this;
  }

  // Animation Settings
  setAnimation(enabled: boolean, duration?: number): this {
    this.config.animation.enabled = enabled;
    if (duration) this.config.animation.duration = duration;
    return this;
  }

  // Responsive Settings
  setResponsive(enabled: boolean): this {
    this.config.responsive = enabled;
    return this;
  }

  // Custom Theme
  setCustomTheme(theme: Partial<BracketTheme>): this {
    this.config.theme = { ...this.config.theme, ...theme };
    return this;
  }

  // Build Configuration
  build(): BracketConfiguration {
    return { ...this.config };
  }

  // Preset Configurations
  static createCompactConfig(participants: number): BracketConfiguration {
    return new BracketConfigurationBuilder()
      .setParticipants(participants)
      .setSpacing('compact')
      .setSizing('small')
      .setTheme('dark')
      .build();
  }

  static createPresentationConfig(participants: number): BracketConfiguration {
    return new BracketConfigurationBuilder()
      .setParticipants(participants)
      .setSpacing('spacious')
      .setSizing('large')
      .setTheme('light')
      .setAnimation(true, 800)
      .build();
  }

  static createPrintConfig(participants: number): BracketConfiguration {
    return new BracketConfigurationBuilder()
      .setParticipants(participants)
      .setSpacing('normal')
      .setSizing('medium')
      .setTheme('light')
      .setAnimation(false)
      .build();
  }
}

// Utility Functions
export const BracketConfigUtils = {
  // Validate configuration
  validateConfig: (config: BracketConfiguration): string[] => {
    const errors: string[] = [];
    
    if (config.participants < 2) {
      errors.push('Minimum 2 participants required');
    }
    
    if (!Number.isInteger(Math.log2(config.participants))) {
      errors.push('Participant count must be a power of 2');
    }
    
    return errors;
  },

  // Get optimal configuration for screen size
  getOptimalConfigForScreen: (participants: number, screenWidth: number): BracketConfiguration => {
    if (screenWidth < 768) {
      return BracketConfigurationBuilder.createCompactConfig(participants);
    } else if (screenWidth < 1280) {
      return new BracketConfigurationBuilder()
        .setParticipants(participants)
        .setSpacing('normal')
        .setSizing('medium')
        .build();
    } else {
      return new BracketConfigurationBuilder()
        .setParticipants(participants)
        .setSpacing('comfortable')
        .setSizing('large')
        .build();
    }
  },

  // Export configuration as JSON
  exportConfig: (config: BracketConfiguration): string => {
    return JSON.stringify(config, null, 2);
  },

  // Import configuration from JSON
  importConfig: (jsonString: string): BracketConfiguration => {
    try {
      const imported = JSON.parse(jsonString);
      return { ...DEFAULT_BRACKET_CONFIG, ...imported };
    } catch {
      return DEFAULT_BRACKET_CONFIG;
    }
  }
};

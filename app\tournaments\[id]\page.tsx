import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import TournamentBracket from "@/components/tournament-bracket"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export default function TournamentPage({ params }: { params: { id: string } }) {
  return (
    <div className="min-h-screen bg-[#333333] text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <div className="flex items-center gap-2 text-sm text-gray-400 mb-2">
              <Link href="/tournaments" className="hover:text-white">
                Tournaments
              </Link>
              <span>/</span>
              <span>Summer Gaming Championship</span>
            </div>
            <h1 className="text-3xl font-bold">Summer Gaming Championship</h1>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="border-gray-700 hover:bg-gray-700">
              Share
            </Button>
            <Button className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white">Register</Button>
          </div>
        </div>

        <div className="bg-[#2a2a2a] rounded-lg p-6 mb-8">
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <h3 className="font-semibold mb-2">Tournament Details</h3>
              <div className="grid gap-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Game:</span>
                  <span>Valorant</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Format:</span>
                  <span>Double Elimination</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Participants:</span>
                  <span>32 (16 registered)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Start Date:</span>
                  <span>June 15, 2025</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Status:</span>
                  <span className="text-[#ff7f32]">Registration Open</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Organizer</h3>
              <div className="flex items-center gap-3 mb-4">
                <Avatar className="h-10 w-10">
                  <AvatarImage src="/placeholder.svg" alt="GamerLeague" />
                  <AvatarFallback>GL</AvatarFallback>
                </Avatar>
                <div>
                  <div className="font-medium">GamerLeague</div>
                  <div className="text-sm text-gray-400">100+ tournaments hosted</div>
                </div>
              </div>
              <p className="text-sm text-gray-400">
                Official tournament organizer for competitive gaming events across multiple titles.
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Prize Pool</h3>
              <div className="text-2xl font-bold mb-2">$5,000</div>
              <div className="grid gap-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">1st Place:</span>
                  <span>$2,500</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">2nd Place:</span>
                  <span>$1,500</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">3rd Place:</span>
                  <span>$1,000</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <Tabs defaultValue="bracket" className="w-full">
          <TabsList className="bg-[#2a2a2a] border-b border-gray-700 w-full justify-start rounded-none mb-6">
            <TabsTrigger value="bracket" className="data-[state=active]:bg-[#333333]">
              Bracket
            </TabsTrigger>
            <TabsTrigger value="participants" className="data-[state=active]:bg-[#333333]">
              Participants
            </TabsTrigger>
            <TabsTrigger value="matches" className="data-[state=active]:bg-[#333333]">
              Matches
            </TabsTrigger>
            <TabsTrigger value="rules" className="data-[state=active]:bg-[#333333]">
              Rules
            </TabsTrigger>
          </TabsList>

          <TabsContent value="bracket" className="mt-0">
            <div className="bg-[#2a2a2a] rounded-lg p-4">
              <TournamentBracket />
            </div>
          </TabsContent>

          <TabsContent value="participants" className="mt-0">
            <div className="bg-[#2a2a2a] rounded-lg p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-semibold">Registered Participants (16/32)</h3>
                <Button variant="outline" size="sm" className="border-gray-700 hover:bg-gray-700">
                  Export List
                </Button>
              </div>

              <div className="grid gap-2">
                {Array.from({ length: 16 }).map((_, i) => (
                  <div key={i} className="flex items-center justify-between p-3 bg-[#333333] rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center text-xs">
                        {i + 1}
                      </div>
                      <Avatar className="h-8 w-8">
                        <AvatarImage src="/placeholder.svg" alt={`Player ${i + 1}`} />
                        <AvatarFallback>P{i + 1}</AvatarFallback>
                      </Avatar>
                      <div className="font-medium">
                        {[
                          "Narda",
                          "JMPlays",
                          "macmacus",
                          "cus",
                          "Corneliax",
                          "Ruiz",
                          "XerxesRegulus",
                          "RadicalPugtato",
                          "Altheare",
                        ][i % 9] || `Player${i + 1}`}
                      </div>
                    </div>
                    <div className="text-sm text-gray-400">Registered on June {i + 1}, 2025</div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="matches" className="mt-0">
            <div className="bg-[#2a2a2a] rounded-lg p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="font-semibold">Upcoming Matches</h3>
                <Button variant="outline" size="sm" className="border-gray-700 hover:bg-gray-700">
                  Calendar View
                </Button>
              </div>

              <div className="grid gap-4">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div key={i} className="p-4 bg-[#333333] rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-sm text-gray-400">Round 1 • Match {i + 1}</div>
                      <div className="text-sm text-[#ff7f32]">June 15, 2025 • 18:00 UTC</div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="/placeholder.svg" alt="Player A" />
                          <AvatarFallback>A</AvatarFallback>
                        </Avatar>
                        <div className="font-medium">
                          {[
                            "Narda",
                            "JMPlays",
                            "macmacus",
                            "cus",
                            "Corneliax",
                            "Ruiz",
                            "XerxesRegulus",
                            "RadicalPugtato",
                          ][i] || `Player A`}
                        </div>
                      </div>

                      <div className="text-center px-4">
                        <div className="text-lg font-bold">VS</div>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="font-medium text-right">
                          {[
                            "RadicalPugtato",
                            "XerxesRegulus",
                            "Ruiz",
                            "Corneliax",
                            "cus",
                            "macmacus",
                            "JMPlays",
                            "Narda",
                          ][i] || `Player B`}
                        </div>
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="/placeholder.svg" alt="Player B" />
                          <AvatarFallback>B</AvatarFallback>
                        </Avatar>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="rules" className="mt-0">
            <div className="bg-[#2a2a2a] rounded-lg p-6">
              <h3 className="font-semibold text-xl mb-4">Tournament Rules</h3>

              <div className="prose prose-invert max-w-none">
                <h4>Format</h4>
                <p>
                  This tournament follows a double elimination format. All matches are best-of-three (Bo3) except for
                  the Grand Finals which will be best-of-five (Bo5).
                </p>

                <h4>Registration</h4>
                <p>
                  Registration closes 24 hours before the tournament start time. All participants must check in 30
                  minutes before their scheduled match time.
                </p>

                <h4>Match Rules</h4>
                <ul>
                  <li>All matches will be played on the latest patch</li>
                  <li>Map selection follows a ban-pick-ban-pick format with the higher seed choosing first</li>
                  <li>Players have 10 minutes to join the lobby after match announcement</li>
                  <li>Disconnections: If a player disconnects, the round will be replayed</li>
                </ul>

                <h4>Code of Conduct</h4>
                <p>
                  All participants are expected to behave in a sportsmanlike manner. Any form of cheating, abuse, or
                  unsportsmanlike conduct will result in immediate disqualification.
                </p>

                <h4>Disputes</h4>
                <p>In case of disputes, tournament administrators have the final say. All decisions are binding.</p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

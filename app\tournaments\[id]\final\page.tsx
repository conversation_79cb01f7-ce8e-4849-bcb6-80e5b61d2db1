"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { DynamicSEBracket } from "@/components/DynamicSEBracket";
import "@/styles/dynamicBracket.css";
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (...args: any[]) => any;
    lastAutoTable: any;
  }
}

interface TournamentSettings {
  id: string;
  name: string;
  status: string;
  typeOfStage: string;
  groupStageFormat: string;
  groupAdvance: number;
  finalStageFormat: string;
  matchFormat: string;
  maxTeams: number;
  startDate: string;
  endDate: string;
}

interface GroupWinner {
  groupName: string;
  rank: number;
  participant: {
    noReg: string;
    name: string;
    club: string;
  };
  points: number;
  wins: number;
  losses: number;
}

export default function FinalStagePage() {
  const params = useParams();
  const router = useRouter();
  const tournamentId = params.id as string;
  
  const [tournament, setTournament] = useState<TournamentSettings | null>(null);
  const [groupWinners, setGroupWinners] = useState<GroupWinner[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [currentStep, setCurrentStep] = useState(1); // 1: Winners List, 2: Randomization, 3: Bracket
  const [randomizedParticipants, setRandomizedParticipants] = useState<GroupWinner[]>([]);
  const [bracketType, setBracketType] = useState<'single-elimination' | 'double-elimination'>('single-elimination');
  const [randomizationMethod, setRandomizationMethod] = useState<'random' | 'seeding'>('random');
  const [showPdfExportModal, setShowPdfExportModal] = useState(false);
  const [showBracketPdfExportModal, setShowBracketPdfExportModal] = useState(false);
  const [isExportingPdf, setIsExportingPdf] = useState(false);
  const [isLocked, setIsLocked] = useState(false);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  // Fetch tournament settings
  useEffect(() => {
    const fetchTournamentSettings = async () => {
      try {
        const response = await fetch(`/api/tournament/${tournamentId}/settings`);
        if (response.ok) {
          const data = await response.json();
          setTournament(data.tournament);
        } else {
          setError("Failed to fetch tournament settings");
        }
      } catch (err) {
        setError("Error fetching tournament settings");
        console.error(err);
      }
    };

    fetchTournamentSettings();
  }, [tournamentId]);

  // Fetch group winners from localStorage (temporary solution)
  useEffect(() => {
    const fetchGroupWinners = () => {
      try {
        // Get group standings from localStorage
        const standingsData = localStorage.getItem(`standings_${tournamentId}`);
        if (standingsData) {
          const standings = JSON.parse(standingsData);
          const winners: GroupWinner[] = [];
          
          // Extract winners based on groupAdvance setting
          Object.keys(standings).forEach(groupName => {
            const groupStandings = standings[groupName];
            const advanceCount = tournament?.groupAdvance || 2;
            
            // Take top N participants from each group
            for (let i = 0; i < Math.min(advanceCount, groupStandings.length); i++) {
              const participant = groupStandings[i];
              winners.push({
                groupName,
                rank: i + 1,
                participant: {
                  noReg: participant.noReg,
                  name: participant.name,
                  club: participant.club
                },
                points: participant.points,
                wins: participant.wins,
                losses: participant.losses
              });
            }
          });
          
          setGroupWinners(winners);
        }
        setLoading(false);
      } catch (err) {
        setError("Error loading group winners");
        setLoading(false);
        console.error(err);
      }
    };

    if (tournament) {
      fetchGroupWinners();
      // Set bracket type based on tournament settings
      if (tournament.finalStageFormat === 'double') {
        setBracketType('double-elimination');
      } else {
        setBracketType('single-elimination');
      }
    }
  }, [tournament, tournamentId]);

  // Function to export PDF
  const exportGroupStagePdf = async () => {
    setIsExportingPdf(true);
    try {
      const doc = new jsPDF();
      let y = 20;

      // Header
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('LAPORAN HASIL GROUP STAGE', 105, y, { align: 'center' });
      y += 15;

      // Tournament info
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(`Tournament: ${tournament?.name || 'Unknown Tournament'}`, 20, y);
      y += 8;
      doc.text(`Status: ${tournament?.status || '-'}`, 20, y);
      y += 8;
      doc.text(`Type: ${tournament?.typeOfStage || '-'}`, 20, y);
      y += 8;
      doc.text(`Final Format: ${tournament?.finalStageFormat || '-'}`, 20, y);
      y += 15;

      // Group Winners Table
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('PEMENANG GROUP STAGE', 20, y);
      y += 10;

      // Prepare data for table
      const tableData = groupWinners.map((winner, index) => [
        index + 1,
        winner.groupName,
        winner.participant.noReg || '-',
        winner.participant.name,
        winner.participant.club || '-',
        winner.points,
        winner.wins,
        winner.losses,
        `${winner.wins - winner.losses}`,
        winner.rank
      ]);

      // Create table
      autoTable(doc, {
        head: [['No', 'Group', 'No Reg', 'Nama Peserta', 'Club/Gardu', 'Poin', 'Menang', 'Kalah', 'Selisih', 'Rank']],
        body: tableData,
        startY: y,
        theme: 'grid',
        headStyles: {
          fillColor: [255, 127, 50], // Orange color
          textColor: 255,
          fontSize: 10,
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 8,
          cellPadding: 2,
          halign: 'center'
        },
        columnStyles: {
          1: { halign: 'center', fontSize: 8 }, // Group align center
          3: { halign: 'left', fontSize: 8 }, // Nama peserta align left
          4: { halign: 'left', fontSize: 8 }  // Club align left
        },
        margin: { left: 15, right: 15 }
      });

      // Footer info
      y = doc.lastAutoTable.finalY + 20;
      const now = new Date();
      const dateStr = now.toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      const timeStr = now.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      doc.setFontSize(9);
      doc.setFont('helvetica', 'normal');
      doc.text('INFORMASI EXPORT:', 20, y);
      y += 7;
      doc.text(`Tanggal Export: ${dateStr}`, 20, y);
      y += 7;
      doc.text(`Waktu Export: ${timeStr}`, 20, y);
      y += 7;
      doc.text(`Total Pemenang: ${groupWinners.length} peserta`, 20, y);

      // Save PDF
      doc.save(`Laporan_Group_Stage_${tournament?.name?.replace(/\s+/g, '_') || 'Tournament'}_${dateStr.replace(/\s+/g, '_')}.pdf`);

      // Show success message
      alert('PDF berhasil diekspor dan didownload!');

    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Gagal mengekspor PDF. Silakan coba lagi.');
    } finally {
      setIsExportingPdf(false);
    }
  };

  // Function to handle proceed to randomization with PDF export
  const handleProceedToRandomization = () => {
    setShowPdfExportModal(true);
  };

  // Function to handle PDF export confirmation
  const handlePdfExportConfirm = async () => {
    setShowPdfExportModal(false);
    await exportGroupStagePdf();
    setCurrentStep(2); // Proceed to randomization step
  };

  // Function to handle PDF export cancel
  const handlePdfExportCancel = () => {
    setShowPdfExportModal(false);
  };

  // Function to export Bracket PDF
  const exportBracketPdf = async () => {
    setIsExportingPdf(true);
    try {
      const doc = new jsPDF();
      let y = 20;

      // Header
      doc.setFontSize(16);
      doc.setFont('helvetica', 'bold');
      doc.text('LAPORAN BRACKET FINAL STAGE', 105, y, { align: 'center' });
      y += 15;

      // Tournament info
      doc.setFontSize(12);
      doc.setFont('helvetica', 'normal');
      doc.text(`Tournament: ${tournament?.name || 'Unknown Tournament'}`, 20, y);
      y += 8;
      doc.text(`Status: ${tournament?.status || '-'}`, 20, y);
      y += 8;
      doc.text(`Type: ${tournament?.typeOfStage || '-'}`, 20, y);
      y += 8;
      doc.text(`Final Format: ${tournament?.finalStageFormat || '-'}`, 20, y);
      y += 8;
      doc.text(`Bracket Type: ${bracketType === 'single-elimination' ? 'Single Elimination' : 'Double Elimination'}`, 20, y);
      y += 8;
      doc.text(`Randomization Method: ${randomizationMethod === 'random' ? 'Random Shuffle' : 'Seeding Method'}`, 20, y);
      y += 15;

      // Bracket Participants Table
      doc.setFontSize(14);
      doc.setFont('helvetica', 'bold');
      doc.text('SUSUNAN BRACKET FINAL STAGE', 20, y);
      y += 10;

      // Prepare data for table
      const tableData = randomizedParticipants.map((participant, index) => [
        index + 1,
        `Position ${index + 1}`,
        participant.groupName,
        participant.participant.noReg || '-',
        participant.participant.name,
        participant.participant.club || '-',
        participant.points,
        participant.wins,
        participant.losses,
        participant.rank
      ]);

      // Create table
      autoTable(doc, {
        head: [['No', 'Bracket Position', 'Group Asal', 'No Reg', 'Nama Peserta', 'Club/Gardu', 'Poin', 'Menang', 'Kalah', 'Rank']],
        body: tableData,
        startY: y,
        theme: 'grid',
        headStyles: {
          fillColor: [255, 127, 50], // Orange color
          textColor: 255,
          fontSize: 10,
          fontStyle: 'bold'
        },
        styles: {
          fontSize: 8,
          cellPadding: 2,
          halign: 'center'
        },
        columnStyles: {
          1: { halign: 'center', fontSize: 8 }, // Bracket Position
          2: { halign: 'center', fontSize: 8 }, // Group Asal
          4: { halign: 'left', fontSize: 8 }, // Nama peserta align left
          5: { halign: 'left', fontSize: 8 }  // Club align left
        },
        margin: { left: 15, right: 15 }
      });

      // Footer info
      y = doc.lastAutoTable.finalY + 20;
      const now = new Date();
      const dateStr = now.toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
      const timeStr = now.toLocaleTimeString('id-ID', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });

      doc.setFontSize(9);
      doc.setFont('helvetica', 'normal');
      doc.text('INFORMASI EXPORT:', 20, y);
      y += 7;
      doc.text(`Tanggal Export: ${dateStr}`, 20, y);
      y += 7;
      doc.text(`Waktu Export: ${timeStr}`, 20, y);
      y += 7;
      doc.text(`Total Peserta Bracket: ${randomizedParticipants.length} peserta`, 20, y);
      y += 7;
      doc.text(`Metode Randomisasi: ${randomizationMethod === 'random' ? 'Acak (Random Shuffle)' : 'Seeding (Berdasarkan Ranking)'}`, 20, y);

      // Save PDF
      doc.save(`Laporan_Bracket_Final_${tournament?.name?.replace(/\s+/g, '_') || 'Tournament'}_${dateStr.replace(/\s+/g, '_')}.pdf`);

      // Show success message
      alert('PDF Bracket berhasil diekspor dan didownload!');

    } catch (error) {
      console.error('Error exporting Bracket PDF:', error);
      alert('Gagal mengekspor PDF Bracket. Silakan coba lagi.');
    } finally {
      setIsExportingPdf(false);
    }
  };

  // Function to handle proceed to bracket generation with PDF export
  const handleProceedToBracket = () => {
    setShowBracketPdfExportModal(true);
  };

  // Function to handle Bracket PDF export confirmation
  const handleBracketPdfExportConfirm = async () => {
    setShowBracketPdfExportModal(false);
    await exportBracketPdf();
    setCurrentStep(3); // Proceed to bracket step
  };

  // Function to handle Bracket PDF export cancel
  const handleBracketPdfExportCancel = () => {
    setShowBracketPdfExportModal(false);
  };

  // Function to handle lock/unlock
  const handleLock = () => {
    setIsLocked(true);
  };

  const handleUnlock = () => {
    setShowPasswordDialog(true);
  };

  // Function to verify password and unlock
  const handlePasswordSubmit = async () => {
    setPasswordError("");
    try {
      const res = await fetch('/api/user/verify-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password }),
      });
      const data = await res.json();
      if (res.ok && data.success) {
        setIsLocked(false);
        setShowPasswordDialog(false);
        setPassword("");
      } else {
        setPasswordError(data.error || "Password salah. Silakan coba lagi.");
      }
    } catch (error) {
      setPasswordError("Gagal terhubung ke server.");
    }
  };

  const handlePasswordCancel = () => {
    setShowPasswordDialog(false);
    setPassword("");
    setPasswordError("");
  };

  // Function to complete tournament
  const handleCompleteTournament = async () => {
    if (confirm("Apakah Anda yakin ingin menyelesaikan tournament ini? Status akan berubah menjadi 'Completed'.")) {
      try {
        const response = await fetch(`/api/tournament/${params.id}/complete`, {
          method: 'PATCH',
        });

        if (response.ok) {
          alert("Tournament berhasil diselesaikan!");
          router.push('/tournaments?tab=completed');
        } else {
          alert("Gagal menyelesaikan tournament. Silakan coba lagi.");
        }
      } catch (error) {
        console.error('Error completing tournament:', error);
        alert("Terjadi kesalahan. Silakan coba lagi.");
      }
    }
  };

  // Random method - shuffle participants
  const randomizeParticipants = () => {
    const shuffled = [...groupWinners];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    setRandomizedParticipants(shuffled);
  };

  // Seeding method - arrange with cross pattern (odd positions = rank 1, even positions = rank 2)
  const seedParticipants = () => {
    // Separate rank 1 and rank 2 participants
    const rank1Participants = groupWinners.filter(p => p.rank === 1);
    const rank2Participants = groupWinners.filter(p => p.rank === 2);

    // Sort rank 1 participants by performance (best to worst)
    const sortedRank1 = [...rank1Participants].sort((a, b) => {
      // Primary: Points (descending)
      if (b.points !== a.points) return b.points - a.points;
      // Secondary: Win ratio (descending)
      const aWinRatio = a.wins / (a.wins + a.losses || 1);
      const bWinRatio = b.wins / (b.wins + b.losses || 1);
      if (bWinRatio !== aWinRatio) return bWinRatio - aWinRatio;
      // Tertiary: Group name for consistency
      return a.groupName.localeCompare(b.groupName);
    });

    // Sort rank 2 participants by performance (worst to best for cross seeding)
    const sortedRank2 = [...rank2Participants].sort((a, b) => {
      // Primary: Points (ascending - worst first for cross pattern)
      if (a.points !== b.points) return a.points - b.points;
      // Secondary: Win ratio (ascending)
      const aWinRatio = a.wins / (a.wins + a.losses || 1);
      const bWinRatio = b.wins / (b.wins + b.losses || 1);
      if (aWinRatio !== bWinRatio) return aWinRatio - bWinRatio;
      // Tertiary: Group name for consistency
      return a.groupName.localeCompare(b.groupName);
    });

    // Create cross pattern: odd positions (1,3,5,7) = rank 1, even positions (2,4,6,8) = rank 2
    const crossSeeded: GroupWinner[] = [];

    for (let i = 0; i < 8; i++) {
      if (i % 2 === 0) {
        // Odd positions (0,2,4,6 = positions 1,3,5,7): place rank 1 participants
        const rank1Index = Math.floor(i / 2);
        if (sortedRank1[rank1Index]) {
          crossSeeded.push(sortedRank1[rank1Index]);
        }
      } else {
        // Even positions (1,3,5,7 = positions 2,4,6,8): place rank 2 participants
        const rank2Index = Math.floor(i / 2);
        if (sortedRank2[rank2Index]) {
          crossSeeded.push(sortedRank2[rank2Index]);
        }
      }
    }

    setRandomizedParticipants(crossSeeded);
  };

  // Apply selected randomization method
  const applyRandomization = () => {
    if (randomizationMethod === 'random') {
      randomizeParticipants();
    } else {
      seedParticipants();
    }
  };

  // Handle match score updates from dynamic bracket
  const handleMatchUpdate = (matchId: string, score1: number, score2: number) => {
    console.log(`Match ${matchId} updated: ${score1} - ${score2}`);
    // Here you can add logic to save match results to database
  };

  // Handle starting final stage
  const handleStartFinalStage = async () => {
    try {
      setLoading(true);

      // Prepare final stage data
      const finalStageData = {
        tournamentId: params.id,
        participants: randomizedParticipants.map(p => ({
          noReg: p.participant.noReg,
          name: p.participant.name,
          club: p.participant.club,
          rank: p.rank,
          points: p.points
        })),
        bracketType,
        randomizationMethod,
        status: 'active'
      };

      // Save final stage configuration
      const response = await fetch(`/api/tournaments/${params.id}/final-stage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(finalStageData),
      });

      if (!response.ok) {
        throw new Error('Failed to start final stage');
      }

      // Redirect to active bracket page
      router.push(`/tournaments/${params.id}/bracket`);

    } catch (error) {
      console.error('Error starting final stage:', error);
      setError('Failed to start final stage. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Update bracket configuration when participants change
  useEffect(() => {
    if (randomizedParticipants.length === 8) {
      console.log('Participants ready for dynamic bracket:', randomizedParticipants.length);
    }
  }, [randomizedParticipants]);



  if (loading) {
    return (
      <div className="min-h-screen bg-[#1a1a1a] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#ff7f32] mx-auto mb-4"></div>
          <p>Loading final stage...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#1a1a1a] text-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-[#ff7f32] hover:bg-[#e86a1f] px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#333333] text-white p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Final Stage</h1>
          <h2 className="text-xl text-gray-300">{tournament?.name}</h2>
          <div className="flex gap-4 mt-4 text-sm">
            <span className="bg-[#2a2a2a] px-3 py-1 rounded">
              Format: {tournament?.finalStageFormat?.toUpperCase()}
            </span>
            <span className="bg-[#2a2a2a] px-3 py-1 rounded">
              Advance per group: {tournament?.groupAdvance}
            </span>
            <span className="bg-[#2a2a2a] px-3 py-1 rounded">
              Total qualified: {groupWinners.length}
            </span>
          </div>
        </div>

        {/* Step Indicator */}
        <div className="flex items-center justify-center mb-8">
          {[1, 2, 3].map((step, index) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                currentStep >= step ? 'bg-[#ff7f32] text-white' : 'bg-gray-600 text-gray-300'
              }`}>
                {step}
              </div>
              {index < 2 && (
                <div className={`w-16 h-1 ${
                  currentStep > step ? 'bg-[#ff7f32]' : 'bg-gray-600'
                }`}></div>
              )}
            </div>
          ))}
        </div>

        {/* Step 1: Group Winners List */}
        {currentStep === 1 && (
          <div className="bg-[#2a2a2a] rounded-lg p-6">
            <h3 className="text-xl font-bold mb-4">Step 1: Group Stage Winners</h3>
            <p className="text-gray-300 mb-6">
              Berikut adalah daftar pemenang dari setiap grup yang akan melanjutkan ke final stage:
            </p>

            <div className="grid gap-4">
              {Object.entries(
                groupWinners.reduce((acc, winner) => {
                  if (!acc[winner.groupName]) acc[winner.groupName] = [];
                  acc[winner.groupName].push(winner);
                  return acc;
                }, {} as Record<string, GroupWinner[]>)
              ).map(([groupName, winners]) => (
                <div key={groupName} className="bg-[#333333] rounded-lg p-4">
                  <h4 className="font-bold text-[#ff7f32] mb-3">{groupName}</h4>
                  <div className="space-y-2">
                    {winners.map((winner, index) => (
                      <div key={index} className="flex justify-between items-center bg-[#404040] p-3 rounded">
                        <div className="flex items-center gap-4">
                          <span className="bg-[#ff7f32] text-white px-2 py-1 rounded text-sm font-bold">
                            #{winner.rank}
                          </span>
                          <div>
                            <p className="font-semibold">{winner.participant.name}</p>
                            <p className="text-sm text-gray-400">{winner.participant.club}</p>
                          </div>
                        </div>
                        <div className="text-right text-sm">
                          <p>{winner.points} pts</p>
                          <p className="text-gray-400">{winner.wins}W-{winner.losses}L</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-end mt-6">
              <button
                onClick={handleProceedToRandomization}
                className="bg-[#ff7f32] hover:bg-[#e86a1f] disabled:bg-gray-500 disabled:cursor-not-allowed text-white px-6 py-2 rounded font-semibold"
                disabled={groupWinners.length === 0 || isLocked}
              >
                Proceed to Randomization
              </button>
            </div>
          </div>
        )}

        {/* Step 2: Bracket Randomization */}
        {currentStep === 2 && (
          <div className="bg-[#2a2a2a] rounded-lg p-6">
            <h3 className="text-xl font-bold mb-2">Step 2: Bracket Randomization</h3>
            <p className="text-gray-300 mb-6">
              Pilih metode pengaturan peserta untuk bracket {bracketType === 'single-elimination' ? 'Single Elimination' : 'Double Elimination'}:
            </p>

            {/* Randomization Method Selection */}
            <div className="mb-6">
              <h4 className="font-bold text-[#ff7f32] mb-4">Metode Pengaturan Peserta</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Random Method */}
                <div className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                  randomizationMethod === 'random'
                    ? 'border-[#ff7f32] bg-[#ff7f32]/10'
                    : 'border-gray-600 bg-[#333333] hover:border-gray-500'
                }`}
                onClick={() => setRandomizationMethod('random')}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <input
                      type="radio"
                      name="randomizationMethod"
                      value="random"
                      checked={randomizationMethod === 'random'}
                      onChange={() => setRandomizationMethod('random')}
                      className="w-4 h-4 text-[#ff7f32]"
                    />
                    <h5 className="font-semibold text-lg">🎲 Random Method</h5>
                  </div>
                  <p className="text-sm text-gray-300 mb-2">
                    Peserta diacak secara acak tanpa mempertimbangkan performa group stage.
                  </p>
                  <p className="text-xs text-gray-400">
                    • Purely random shuffle<br/>
                    • No performance consideration<br/>
                    • Equal chance for all participants
                  </p>
                </div>

                {/* Seeding Method */}
                <div className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                  randomizationMethod === 'seeding'
                    ? 'border-[#ff7f32] bg-[#ff7f32]/10'
                    : 'border-gray-600 bg-[#333333] hover:border-gray-500'
                }`}
                onClick={() => setRandomizationMethod('seeding')}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <input
                      type="radio"
                      name="randomizationMethod"
                      value="seeding"
                      checked={randomizationMethod === 'seeding'}
                      onChange={() => setRandomizationMethod('seeding')}
                      className="w-4 h-4 text-[#ff7f32]"
                    />
                    <h5 className="font-semibold text-lg">🏆 Seeding Method</h5>
                  </div>
                  <p className="text-sm text-gray-300 mb-2">
                    Peserta diatur dengan pola seeding silang untuk bracket yang seimbang.
                  </p>
                  <p className="text-xs text-gray-400">
                    • <span className="text-[#ff7f32] font-semibold">Posisi Ganjil (1,3,5,7):</span> Rank 1 (pemenang grup)<br/>
                    • <span className="text-[#ff7f32] font-semibold">Posisi Genap (2,4,6,8):</span> Rank 2 (runner-up grup)<br/>
                    • Cross pattern untuk bracket seimbang
                  </p>
                </div>
              </div>

              {/* Apply Button */}

            </div>

            {/* Bracket Type Display */}
            <div className="mb-6">
              <div className="bg-[#333333] rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="font-bold text-[#ff7f32] mb-2">Bracket Format</h4>
                    <p className="text-lg font-semibold">
                      {bracketType === 'single-elimination' ? 'Single Elimination' : 'Double Elimination'}
                    </p>
                    <p className="text-sm text-gray-400 mt-1">
                      {bracketType === 'single-elimination'
                        ? 'Peserta yang kalah akan langsung tersingkir'
                        : 'Peserta mendapat kesempatan kedua di losers bracket'
                      }
                    </p>
                  </div>
                  <button
                    onClick={applyRandomization}
                    disabled={isLocked}
                    className="bg-[#ff7f32] hover:bg-[#e86a1f] disabled:bg-gray-500 disabled:cursor-not-allowed text-white px-4 py-2 rounded font-semibold"
                  >
                    Re-shuffle
                  </button>
                </div>
              </div>
            </div>

            {/* Randomized/Seeded Participants */}
            {randomizedParticipants.length > 0 && (
              <div className="mb-6">
                <h4 className="font-bold text-[#ff7f32] mb-4">
                  Urutan Peserta ({randomizationMethod === 'random' ? 'Setelah Randomization' : 'Setelah Seeding'})
                </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {randomizedParticipants.map((participant, index) => (
                  <div key={index} className="bg-[#333333] rounded-lg p-4 flex items-center gap-4">
                    <div className="bg-[#ff7f32] text-white px-3 py-2 rounded-lg font-bold text-lg">
                      #{index + 1}
                    </div>
                    <div className="flex-1">
                      <p className="font-semibold">{participant.participant.name}</p>
                      <p className="text-sm text-gray-400">{participant.participant.club}</p>
                      <p className="text-xs text-gray-500">
                        {participant.groupName} - Rank #{participant.rank}
                      </p>
                    </div>
                    <div className="text-right text-sm">
                      <p className="font-semibold">{participant.points} pts</p>
                      <p className="text-gray-400">{participant.wins}W-{participant.losses}L</p>
                    </div>
                  </div>
                ))}
              </div>
              </div>
            )}



            <div className="flex justify-between">
              <button
                onClick={() => setCurrentStep(1)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded"
              >
                Back
              </button>
              <button
                onClick={handleProceedToBracket}
                className="bg-[#ff7f32] hover:bg-[#e86a1f] disabled:bg-gray-500 disabled:cursor-not-allowed text-white px-6 py-2 rounded font-semibold"
                disabled={randomizedParticipants.length === 0 || isLocked}
              >
                Generate Bracket
              </button>
            </div>
          </div>
        )}



        {/* Step 3: Bracket Display */}
        {currentStep === 3 && (
          <div className="bg-[#2a2a2a] rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h3 className="text-xl font-bold mb-2">Step 3: Tournament Bracket</h3>
                <p className="text-gray-300">
                  Bracket {bracketType === 'single-elimination' ? 'Single Elimination' : 'Double Elimination'} dengan pola seeding silang:
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={handleStartFinalStage}
                  disabled={loading || randomizedParticipants.length === 0 || isLocked}
                  className="bg-[#ff7f32] hover:bg-[#e86a1f] disabled:bg-gray-500 disabled:cursor-not-allowed text-white px-6 py-2 rounded font-semibold"
                >
                  {loading ? 'Starting...' : 'Start Final Stage'}
                </button>

                {/* Lock/Unlock buttons */}
                {isLocked ? (
                  <button
                    onClick={handleUnlock}
                    className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded font-semibold transition-colors"
                  >
                    Open
                  </button>
                ) : (
                  <button
                    onClick={handleLock}
                    className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded font-semibold transition-colors"
                  >
                    Lock
                  </button>
                )}
              </div>
            </div>

            {/* Seeding Pattern Info */}
            <div className="bg-[#333333] rounded-lg p-4 mb-6">
              <h4 className="font-bold text-[#ff7f32] mb-3">🎯 Hasil Pola Seeding Silang</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="font-semibold mb-2">Quarter Finals Matchups:</p>
                  <div className="space-y-1 text-gray-300">
                    <p>• <span className="text-[#ff7f32]">QF1:</span> Seed #1 (Rank 1 terbaik) vs Seed #8 (Rank 2 terlemah)</p>
                    <p>• <span className="text-[#ff7f32]">QF2:</span> Seed #4 (Rank 2 kedua terlemah) vs Seed #5 (Rank 1 ketiga)</p>
                    <p>• <span className="text-[#ff7f32]">QF3:</span> Seed #3 (Rank 1 kedua) vs Seed #6 (Rank 2 ketiga terlemah)</p>
                    <p>• <span className="text-[#ff7f32]">QF4:</span> Seed #2 (Rank 2 terlemah) vs Seed #7 (Rank 1 keempat)</p>
                  </div>
                </div>
                <div>
                  <p className="font-semibold mb-2">Seeding Logic:</p>
                  <div className="space-y-1 text-gray-300">
                    <p>• <span className="text-[#ff7f32]">Posisi Ganjil (1,3,5,7):</span> Rank 1 participants</p>
                    <p>• <span className="text-[#ff7f32]">Posisi Genap (2,4,6,8):</span> Rank 2 participants</p>
                    <p>• Cross pattern untuk bracket seimbang</p>
                    <p>• Pemenang grup vs runner-up silang</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Dynamic Tournament Bracket */}
            {randomizedParticipants.length > 0 && (
              <div className="mb-6">
                <DynamicSEBracket
                  participants={randomizedParticipants.map(p =>
                    `${p.participant.noReg || '000'} ] ${p.participant.name}`
                  )}
                  seedingMethod={randomizationMethod === 'seeding' ? 'seeded' : 'random'}
                  customConfig={{
                    participants: 8,
                    centerAlignment: true,
                    containerWidth: 1200,
                    containerHeight: 800,
                    matchHeight: 80,
                    roundSpacing: 200,
                    verticalSpacing: 96
                  }}
                  onMatchUpdate={handleMatchUpdate}
                  className="bg-[#333333] rounded-lg p-6"
                />
              </div>
            )}

            <div className="flex justify-between">
              <button
                onClick={() => setCurrentStep(2)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded"
              >
                Back
              </button>

              <button
                onClick={handleCompleteTournament}
                disabled={isLocked}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-500 disabled:cursor-not-allowed text-white px-6 py-2 rounded font-semibold"
              >
                Complete Tournament
              </button>
            </div>
          </div>
        )}

        {/* PDF Export Confirmation Modal */}
        {showPdfExportModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-[#2a2a2a] rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-xl font-bold mb-4 text-white">Export PDF Laporan</h3>
              <p className="text-gray-300 mb-6">
                Sebelum melanjutkan ke tahap randomisasi, Anda harus mengekspor laporan PDF dari hasil Group Stage terlebih dahulu.
              </p>
              <div className="flex justify-end space-x-4">
                <button
                  onClick={handlePdfExportCancel}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded font-semibold"
                  disabled={isExportingPdf}
                >
                  Cancel
                </button>
                <button
                  onClick={handlePdfExportConfirm}
                  className="px-4 py-2 bg-[#ff7f32] hover:bg-[#e86a1f] text-white rounded font-semibold"
                  disabled={isExportingPdf}
                >
                  {isExportingPdf ? 'Exporting...' : 'Save & Export PDF'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Bracket PDF Export Confirmation Modal */}
        {showBracketPdfExportModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-[#2a2a2a] rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-xl font-bold mb-4 text-white">Export PDF Bracket</h3>
              <p className="text-gray-300 mb-6">
                Sebelum melanjutkan ke tahap bracket, Anda harus mengekspor laporan PDF dari susunan bracket final stage terlebih dahulu.
              </p>
              <div className="flex justify-end space-x-4">
                <button
                  onClick={handleBracketPdfExportCancel}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded font-semibold"
                  disabled={isExportingPdf}
                >
                  Cancel
                </button>
                <button
                  onClick={handleBracketPdfExportConfirm}
                  className="px-4 py-2 bg-[#ff7f32] hover:bg-[#e86a1f] text-white rounded font-semibold"
                  disabled={isExportingPdf}
                >
                  {isExportingPdf ? 'Exporting...' : 'Save & Export PDF'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Password Dialog Modal */}
        {showPasswordDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-[#2a2a2a] rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-xl font-bold mb-4 text-white">Masukkan Password</h3>
              <p className="text-gray-300 mb-4">
                Masukkan password untuk membuka kunci halaman ini.
              </p>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Password"
                className="w-full px-3 py-2 bg-[#333333] border border-gray-600 rounded text-white mb-4 focus:outline-none focus:border-[#ff7f32]"
                onKeyDown={(e) => e.key === 'Enter' && handlePasswordSubmit()}
              />
              {passwordError && (
                <p className="text-red-500 text-sm mb-4">{passwordError}</p>
              )}
              <div className="flex justify-end space-x-4">
                <button
                  onClick={handlePasswordCancel}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded font-semibold"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePasswordSubmit}
                  className="px-4 py-2 bg-[#ff7f32] hover:bg-[#e86a1f] text-white rounded font-semibold"
                >
                  Unlock
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

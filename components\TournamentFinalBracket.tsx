'use client';

import React, { useState } from 'react';

interface Participant {
  name: string;
  regNumber: string;
  score?: number;
  winner?: boolean;
}

interface Match {
  id: number;
  participant1: Participant | null;
  participant2: Participant | null;
  round: number;
  position: number;
}

interface TournamentFinalBracketProps {
  participants: string[];
  onMatchUpdate?: (matchId: string, score1: number, score2: number) => void;
  seedingMethod?: 'random' | 'seeded';
}

const TournamentFinalBracket: React.FC<TournamentFinalBracketProps> = ({
  participants,
  onMatchUpdate,
  seedingMethod = 'random'
}) => {
  const [matchScores, setMatchScores] = useState<{[key: string]: {score1: number, score2: number}}>({});

  // Parse participants from format "0001 ] Player Name"
  const parseParticipant = (participantStr: string): Participant => {
    if (!participantStr || participantStr === 'TBD') {
      return { name: 'TBD', regNumber: '000' };
    }

    const match = participantStr.match(/^(\d+)\s*\]\s*(.+)$/);
    if (match) {
      return {
        name: match[2].trim(),
        regNumber: match[1]
      };
    }

    return { name: participantStr, regNumber: '000' };
  };

  // Generate matches based on participants
  const generateMatches = (): Match[] => {
    const parsedParticipants = participants.map(parseParticipant);
    const matches: Match[] = [];
    
    // Ensure we have 8 participants (pad with TBD if needed)
    while (parsedParticipants.length < 8) {
      parsedParticipants.push({ name: 'TBD', regNumber: '000' });
    }

    // Quarter Finals (Round 1)
    for (let i = 0; i < 4; i++) {
      matches.push({
        id: i + 1,
        participant1: parsedParticipants[i * 2] || null,
        participant2: parsedParticipants[i * 2 + 1] || null,
        round: 1,
        position: i
      });
    }

    // Semi Finals (Round 2)
    for (let i = 0; i < 2; i++) {
      matches.push({
        id: i + 5,
        participant1: { name: 'Winner QF' + (i * 2 + 1), regNumber: '000' },
        participant2: { name: 'Winner QF' + (i * 2 + 2), regNumber: '000' },
        round: 2,
        position: i
      });
    }

    // Final (Round 3)
    matches.push({
      id: 7,
      participant1: { name: 'Winner SF1', regNumber: '000' },
      participant2: { name: 'Winner SF2', regNumber: '000' },
      round: 3,
      position: 0
    });

    return matches;
  };

  const matches = generateMatches();

  const getMatchesByRound = (round: number) => {
    return matches.filter(match => match.round === round);
  };

  const handleScoreUpdate = (matchId: number, participantIndex: number, score: number) => {
    const key = `match-${matchId}`;
    const currentScores = matchScores[key] || { score1: 0, score2: 0 };
    
    if (participantIndex === 0) {
      currentScores.score1 = score;
    } else {
      currentScores.score2 = score;
    }
    
    setMatchScores(prev => ({
      ...prev,
      [key]: currentScores
    }));

    if (onMatchUpdate) {
      onMatchUpdate(key, currentScores.score1, currentScores.score2);
    }
  };

  const renderParticipant = (participant: Participant | null, matchId: number, participantIndex: number) => {
    if (!participant) {
      return (
        <div className="flex items-center justify-between p-3 bg-[#404040] text-gray-400">
          <span className="text-sm">TBD</span>
          <span className="font-mono text-xs">-</span>
        </div>
      );
    }

    const matchKey = `match-${matchId}`;
    const scores = matchScores[matchKey] || { score1: 0, score2: 0 };
    const score = participantIndex === 0 ? scores.score1 : scores.score2;

    return (
      <div className="flex items-center justify-between px-3 py-2 bg-[#404040] text-white hover:bg-[#4a4a4a] transition-colors">
        <div className="flex-1 min-w-0 ml-5">
          <p className="text-xs font-medium text-white">
            {participant.regNumber} ] {participant.name}
          </p>
        </div>
        <input
          type="number"
          className="w-12 h-8 ml-2 rounded bg-[#333] text-center text-sm border border-gray-600 text-white font-bold"
          min={0}
          value={score || ''}
          onChange={(e) => handleScoreUpdate(matchId, participantIndex, parseInt(e.target.value) || 0)}
          disabled={participant.name === 'TBD' || participant.name.startsWith('Winner')}
        />
      </div>
    );
  };

  const renderConnectionLines = (matchPosition: number, isLastRound: boolean = false) => {
    if (isLastRound) return null;

    const isEvenMatch = matchPosition % 2 === 0;

    return (
      <div className="absolute left-full top-1/2 transform -translate-y-1/2 pointer-events-none z-0">
        {/* Horizontal line from match */}
        <div
          className="absolute top-0 left-0 bg-gray-400"
          style={{
            width: '32px',
            height: '2px',
            transform: 'translateY(-1px)'
          }}
        />

        {/* Vertical connector line */}
        {isEvenMatch ? (
          // Top match - line goes down
          <div
            className="absolute left-8 top-0 bg-gray-400"
            style={{
              width: '2px',
              height: '90px',
              transform: 'translateY(-1px)'
            }}
          />
        ) : (
          // Bottom match - line goes up
          <div
            className="absolute left-8 bg-gray-400"
            style={{
              width: '2px',
              height: '90px',
              transform: 'translateY(-89px)'
            }}
          />
        )}

        {/* Horizontal line to next match (only for top match of pair) */}
        {isEvenMatch && (
          <div
            className="absolute left-8 bg-gray-400"
            style={{
              width: '32px',
              height: '2px',
              top: '89px',
              transform: 'translateY(-1px)'
            }}
          />
        )}
      </div>
    );
  };

  const renderMatch = (match: Match, isLastRound: boolean = false) => {
    return (
      <div key={match.id} className="relative">
        {/* Connection Lines */}
        {renderConnectionLines(match.position, isLastRound)}

        {/* Match Container */}
        <div className="border border-gray-600 rounded-lg overflow-hidden bg-[#404040] relative z-10 shadow-lg">
          {/* Participants */}
          <div>
            {/* Participant 1 */}
            <div className="border-b border-gray-600">
              {renderParticipant(match.participant1, match.id, 0)}
            </div>

            {/* Match ID - positioned between participants */}
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-xs font-bold z-20">
              {match.id}
            </div>

            {/* Participant 2 */}
            <div>
              {renderParticipant(match.participant2, match.id, 1)}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full overflow-x-auto bg-[#333333] p-6 rounded-lg">
      <div className="min-w-[1000px]">
        {/* Round Headers */}
        <div className="grid grid-cols-3 gap-8 mb-6 border-b border-gray-700 pb-4">
          <div className="font-bold text-center text-[#ff7f32] text-lg">Quarter Finals</div>
          <div className="font-bold text-center text-[#ff7f32] text-lg">Semi Finals</div>
          <div className="font-bold text-center text-[#ff7f32] text-lg">Final</div>
        </div>

        {/* Bracket Layout */}
        <div className="grid grid-cols-3 gap-16 relative" style={{ minHeight: '600px' }}>
          {/* Round 1 - Quarter Finals */}
          <div className="relative">
            {getMatchesByRound(1).map((match, index) => (
              <div key={match.id} style={{ position: 'absolute', top: `${index * 120}px`, width: '100%' }}>
                {renderMatch(match, false)}
              </div>
            ))}
          </div>

          {/* Round 2 - Semi Finals */}
          <div className="relative">
            {getMatchesByRound(2).map((match, index) => (
              <div key={match.id} style={{ position: 'absolute', top: `${60 + (index * 300)}px`, width: '100%' }}>
                {renderMatch(match, false)}
              </div>
            ))}
          </div>

          {/* Round 3 - Final */}
          <div className="relative">
            {getMatchesByRound(3).map((match, index) => (
              <div key={match.id} style={{ position: 'absolute', top: `210px`, width: '100%' }}>
                {renderMatch(match, true)}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TournamentFinalBracket;

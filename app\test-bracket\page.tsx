'use client';

import React, { useState } from 'react';
import { DynamicSEBracket } from '@/components/DynamicSEBracket';

export default function TestBracketPage() {
  // Sample participants data untuk testing
  const [participants] = useState([
    '0001 ] Atlet 001',
    '0002 ] Atlet 002', 
    '0003 ] Atlet 003',
    '0004 ] Atlet 004',
    '0005 ] Atlet 005',
    '0006 ] Atlet 006',
    '0007 ] Atlet 007',
    '0008 ] Atlet 008'
  ]);

  const [seedingMethod, setSeedingMethod] = useState<'random' | 'seeded'>('random');

  const handleMatchUpdate = (matchId: string, score1: number, score2: number) => {
    console.log(`Match ${matchId}: ${score1} - ${score2}`);
  };

  return (
    <div className="min-h-screen bg-[#222222] text-white">
      {/* Header */}
      <div className="bg-[#333333] border-b border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-[#ff7f32] mb-2">
            Test Dynamic SE Bracket
          </h1>
          <p className="text-gray-300">
            Halaman test untuk melihat tampilan DynamicSEBracket.tsx (Single Elimination)
          </p>
        </div>
      </div>

      {/* Controls */}
      <div className="container mx-auto px-4 py-6">
        <div className="bg-[#333333] rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-4">Bracket Settings</h3>
          <div className="flex items-center gap-4">
            <label className="text-sm text-gray-300">Seeding Method:</label>
            <select 
              value={seedingMethod}
              onChange={(e) => setSeedingMethod(e.target.value as 'random' | 'seeded')}
              className="bg-[#404040] border border-gray-600 rounded px-3 py-1 text-white"
            >
              <option value="random">Random</option>
              <option value="seeded">Seeded</option>
            </select>
          </div>
        </div>

        {/* Participants List */}
        <div className="bg-[#333333] rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold mb-4">Participants ({participants.length})</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {participants.map((participant, index) => (
              <div key={index} className="bg-[#404040] rounded px-3 py-2 text-sm">
                {participant}
              </div>
            ))}
          </div>
        </div>

        {/* Tournament Bracket */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">Single Elimination Bracket</h3>
          <DynamicSEBracket
            participants={participants}
            seedingMethod={seedingMethod}
            onMatchUpdate={handleMatchUpdate}
            customConfig={{
              participants: 8,
              centerAlignment: true,
              containerWidth: 1200,
              containerHeight: 800,
              matchHeight: 80,
              roundSpacing: 200,
              verticalSpacing: 96
            }}
            className="bg-[#333333] rounded-lg p-6"
          />
        </div>

        {/* Instructions */}
        <div className="bg-[#333333] rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-4">Instructions</h3>
          <ul className="text-sm text-gray-300 space-y-2">
            <li>• Input score pada kolom di sebelah kanan nama participant</li>
            <li>• Match number ditampilkan di sebelah kiri antara kedua participant</li>
            <li>• Format participant: "0001 ] Nama Atlet"</li>
            <li>• Ubah seeding method untuk melihat perbedaan arrangement</li>
            <li>• Check console untuk melihat match updates</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import * as XLSX from "xlsx"
import { shuffle as lodashShuffle } from "lodash"
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

export default function CreateTournamentPage() {
  const [date, setDate] = useState<Date | undefined>(new Date())
  const [step, setStep] = useState(1)
  const [participants, setParticipants] = useState<Array<{
    noReg: string,
    name: string,
    club: string
  }>>([])
  const [form, setForm] = useState({
    name: "",
    status: "upcoming",
    startDate: new Date(),
    endDate: new Date(),
    format: "",
    participants: 2,
    matchFormat: "",
    tentative: false,
    typeOfStage: "single",
    groupStageFormat: "",
    groupParticipants: "",
    groupAdvance: 2,
    finalStageFormat: "",
  })
  const [errors, setErrors] = useState<any>({})
  const [rrGroupInfo, setRRGroupInfo] = useState<{valid: boolean, text: string}>({valid: false, text: ""})
  const [groupValidated, setGroupValidated] = useState(false)
  const [distributedGroups, setDistributedGroups] = useState<Array<Array<{ noReg: string, name: string, club: string }>>>([])
  const [pdfExported, setPdfExported] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const nextStep = () => setStep(step + 1)
  const prevStep = () => setStep(step - 1)

  const validateStep1 = () => {
    const err: any = {}
    if (!form.name || form.name.length < 3) err.name = "Tournament name is required (min 3 chars)"
    if (!form.status) err.status = "Status is required"
    if (!form.tentative) {
      if (!form.startDate) err.startDate = "Start date is required"
      if (!form.endDate) err.endDate = "End date is required"
      if (form.endDate && form.startDate && form.endDate < form.startDate) err.endDate = "End date cannot be before start date"
    }
    setErrors(err)
    return Object.keys(err).length === 0
  }
  const validateStep2 = () => {
    const err: any = {}
    if (!form.typeOfStage) err.typeOfStage = "Type of stage is required"
    if (form.typeOfStage === "single") {
      if (!form.format) err.format = "Format is required"
      // For single stage SE/DE, minimum 8 participants
      if ((form.format === "single" || form.format === "double") && form.participants < 8) {
        err.participants = "Single Elimination/Double Elimination requires minimum 8 participants"
      }
    } else {
      if (!form.groupStageFormat) err.groupStageFormat = "Group stage format is required"
      if (!form.groupAdvance || form.groupAdvance < 1) err.groupAdvance = "Min 1 participant advances"
      if (!form.finalStageFormat) err.finalStageFormat = "Final stage format is required"
      if (!groupValidated) err.groupValidated = "Please validate group distribution"
    }
    if (!form.participants || form.participants < 2) err.participants = "Min 2 participants"
    if (!form.matchFormat) err.matchFormat = "Match format is required"
    setErrors(err)
    return Object.keys(err).length === 0
  }

  let finalStageOptions = [
    { value: "single", label: "Single Elimination" },
    { value: "double", label: "Double Elimination" },
    { value: "round", label: "Round Robin" },
  ];
  if (form.typeOfStage === "two") {
    if (form.groupStageFormat === "round") {
      finalStageOptions = [
        { value: "single", label: "Single Elimination" },
        { value: "double", label: "Double Elimination" },
      ];
    } else if (form.groupStageFormat === "double") {
      finalStageOptions = [
        { value: "single", label: "Single Elimination" },
      ];
    } else if (form.groupStageFormat === "single") {
      finalStageOptions = [
        { value: "single", label: "Single Elimination" },
      ];
    }
  }

  useEffect(() => {
    if (form.typeOfStage === "two") {
      let valid = finalStageOptions.some(opt => opt.value === form.finalStageFormat)
      if (!valid && form.finalStageFormat) {
        setForm(f => ({ ...f, finalStageFormat: "" }))
      }
    }
  }, [form.groupStageFormat, form.typeOfStage])

  useEffect(() => {
    if (form.typeOfStage === "two" && form.groupStageFormat === "round") {
      const dist = getRRGroupDistribution(form.participants)
      setRRGroupInfo(dist)
      setForm(f => ({ ...f, groupParticipants: "" }))
    } else if (form.typeOfStage === "two" && (form.groupStageFormat === "single" || form.groupStageFormat === "double")) {
      const dist = getSEDEGroupDistribution(form.participants)
      setRRGroupInfo(dist)
      setForm(f => ({ ...f, groupParticipants: "" }))
    } else {
      setRRGroupInfo({valid: false, text: ""})
    }
  }, [form.participants, form.typeOfStage, form.groupStageFormat])

  const handleExcelUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    const reader = new FileReader()
    reader.onload = (evt) => {
      const data = evt.target?.result
      if (!data) return
      const workbook = XLSX.read(data, { type: "binary" })
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      const json = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      // Skip header row and process data
      // Filter out empty rows and map to participant data
      const participantsData = json.slice(1)
        .filter((row: any) => row && row[1] && String(row[1]).trim() !== '') // Only include rows with non-empty athlete name
        .map((row: any, index: number) => ({
          noReg: String(index + 1).padStart(3, '0'),
          name: row[1] || '', // Nama Athlet
          club: row[2] || '', // Nama Gardu/Club
        }))

      setParticipants(participantsData)
      setForm(f => ({ ...f, participants: participantsData.length }))
    }
    reader.readAsBinaryString(file)
  }

  // Fungsi untuk distribusi group RR (maksimal grup isi 4, sisanya grup isi 5)
  function getRRGroupDistribution(total: number) {
    if (total < 4) return { valid: false, text: '' }
    const group4 = Math.floor(total / 4)
    const sisa = total % 4
    if (sisa === 0) {
      return { valid: true, text: `${group4} groups with 4 participants` }
    } else {
      return {
        valid: true,
        text: `${group4 - sisa} groups with 4 participants, ${sisa} groups with 5 participants`
      }
    }
  }

  // Fungsi untuk distribusi group SE/DE (min 8, max 32 peserta per grup)
  function getSEDEGroupDistribution(total: number) {
    if (total < 8) return { valid: false, text: '' }

    // Valid group sizes for SE/DE: powers of 2 and powers of 2 + 1 (for BYE handling)
    // 8, 9, 16, 17, 32, 33 (with max 1 BYE per group)
    const validSizes = [8, 9, 16, 17, 32, 33];

    // Try different combinations starting from largest groups
    for (let group32 = 0; group32 <= Math.floor(total / 32); group32++) {
      let remaining = total - group32 * 32;
      if (remaining < 0) continue;

      for (let group33 = 0; group33 <= Math.floor(remaining / 33); group33++) {
        let afterGroup33 = remaining - group33 * 33;
        if (afterGroup33 < 0) continue;

        for (let group16 = 0; group16 <= Math.floor(afterGroup33 / 16); group16++) {
          let afterGroup16 = afterGroup33 - group16 * 16;
          if (afterGroup16 < 0) continue;

          for (let group17 = 0; group17 <= Math.floor(afterGroup16 / 17); group17++) {
            let afterGroup17 = afterGroup16 - group17 * 17;
            if (afterGroup17 < 0) continue;

            for (let group8 = 0; group8 <= Math.floor(afterGroup17 / 8); group8++) {
              let afterGroup8 = afterGroup17 - group8 * 8;
              if (afterGroup8 < 0) continue;

              // Check if remaining can be filled with group9
              if (afterGroup8 % 9 === 0) {
                let group9 = afterGroup8 / 9;
                let parts = [];
                if (group33 > 0) parts.push(`${group33} group${group33 > 1 ? 's' : ''} with 33 participants`);
                if (group32 > 0) parts.push(`${group32} group${group32 > 1 ? 's' : ''} with 32 participants`);
                if (group17 > 0) parts.push(`${group17} group${group17 > 1 ? 's' : ''} with 17 participants`);
                if (group16 > 0) parts.push(`${group16} group${group16 > 1 ? 's' : ''} with 16 participants`);
                if (group9 > 0) parts.push(`${group9} group${group9 > 1 ? 's' : ''} with 9 participants`);
                if (group8 > 0) parts.push(`${group8} group${group8 > 1 ? 's' : ''} with 8 participants`);
                return { valid: true, text: parts.join(', ') };
              }
            }
          }
        }
      }
    }

    return { valid: false, text: '' };
  }

  // Fungsi untuk parsing distribusi grup dari rrGroupInfo.text
  function parseGroupDistribution(text: string) {
    const result: number[] = [];
    const regex = /(\d+) groups? with (\d+) participants/g;
    let match;
    while ((match = regex.exec(text)) !== null) {
      const count = parseInt(match[1]);
      const size = parseInt(match[2]);
      for (let i = 0; i < count; i++) result.push(size);
    }
    return result;
  }

  // Fungsi distribusi utama
  function distributeParticipants(participants: Array<{ noReg: string, name: string, club: string }>, groupSizes: number[], format: string, typeOfStage: string, groupStageFormat: string) {
    // 1. Kelompokkan peserta berdasarkan gardu
    const clubMap: Record<string, Array<{ noReg: string, name: string, club: string }>> = {};
    for (const p of participants) {
      if (!clubMap[p.club]) clubMap[p.club] = [];
      clubMap[p.club].push(p);
    }
    // 2. Urutkan gardu berdasarkan jumlah peserta terbanyak
    const clubsSorted = Object.entries(clubMap).sort((a, b) => b[1].length - a[1].length);
    // 3. Siapkan array grup kosong sesuai groupSizes
    const groups: Array<Array<{ noReg: string, name: string, club: string }>> = groupSizes.map(() => []);

    // 4. Distribusi proporsional untuk double stage SE/DE pada stage 1 (group stage)
    if (typeOfStage === "two" && (groupStageFormat === "single" || groupStageFormat === "double")) {
      for (const [club, athletes] of clubsSorted) {
        const groupCount = groups.length;
        const base = Math.floor(athletes.length / groupCount);
        const extra = athletes.length % groupCount;
        let idx = 0;
        const distribusi = Array(groupCount).fill(base);
        for (let i = 0; i < extra; i++) {
          distribusi[i]++;
        }
        // Distribusi dengan pengecekan kapasitas
        let g = 0;
        for (let i = 0; i < groupCount && idx < athletes.length; i++) {
          let toAdd = distribusi[i];
          while (toAdd > 0 && idx < athletes.length) {
            // Cari grup berikutnya yang masih ada slot
            let tries = 0;
            while (groups[g].length >= groupSizes[g] && tries < groupCount) {
              g = (g + 1) % groupCount;
              tries++;
            }
            if (groups[g].length < groupSizes[g]) {
              groups[g].push(athletes[idx++]);
              toAdd--;
              g = (g + 1) % groupCount;
            } else {
              // Semua grup penuh, break
              break;
            }
          }
        }
      }
    } else if (format === "single" || format === "double") {
      // Single stage SE/DE
      for (const [club, athletes] of clubsSorted) {
        let base = Math.floor(athletes.length / groups.length);
        let extra = athletes.length % groups.length;
        let idx = 0;
        // Setiap grup dapat minimal 'base' peserta dari club ini
        for (let g = 0; g < groups.length; g++) {
          for (let b = 0; b < base; b++) {
            if (athletes[idx]) groups[g].push(athletes[idx++]);
          }
        }
        // Sisa peserta didistribusikan ke grup berbeda
        for (let g = 0; g < groups.length && extra > 0; g++) {
          if (athletes[idx]) groups[g].push(athletes[idx++]);
          extra--;
        }
      }
    } else {
      // RR atau format lain: round robin biasa, tetap jaga tidak ada club sama dalam grup jika bisa
      let groupIdx = 0;
      for (const [club, athletes] of clubsSorted) {
        for (const athlete of athletes) {
          // Cari grup berikutnya yang belum ada dari club ini
          let found = false;
          let startIdx = groupIdx;
          do {
            if (!groups[groupIdx].some(p => p.club === club) && groups[groupIdx].length < groupSizes[groupIdx]) {
              groups[groupIdx].push(athlete);
              found = true;
              groupIdx = (groupIdx + 1) % groups.length;
              break;
            }
            groupIdx = (groupIdx + 1) % groups.length;
          } while (groupIdx !== startIdx);
          // Jika tidak ketemu (semua grup sudah ada club ini), masukkan ke grup berikutnya yang belum penuh
          if (!found) {
            for (let i = 0; i < groups.length; i++) {
              if (groups[i].length < groupSizes[i]) {
                groups[i].push(athlete);
                groupIdx = (i + 1) % groups.length;
                break;
              }
            }
          }
        }
      }
    }
    // 5. Acak internal grup
    for (let i = 0; i < groups.length; i++) {
      groups[i] = lodashShuffle(groups[i]);
    }
    return groups;
  }

  // Handler untuk distribusi awal dan re-Shuffle
  function handleDistributeOrShuffle() {
    const groupSizes = parseGroupDistribution(rrGroupInfo.text);
    // format: single/double/round
    const format = form.format;
    const typeOfStage = form.typeOfStage;
    const groupStageFormat = form.groupStageFormat;
    const groups = distributeParticipants(participants, groupSizes, format, typeOfStage, groupStageFormat);
    setDistributedGroups(groups);
  }

  // Otomatis distribusi saat peserta atau group info berubah
  useEffect(() => {
    if (participants.length && rrGroupInfo.text) {
      handleDistributeOrShuffle();
    }
    // eslint-disable-next-line
  }, [participants, rrGroupInfo.text]);

  // Render grup hasil distribusi
  const renderDistributedGroups = () => (
    <div className="grid grid-cols-2 gap-4">
      {distributedGroups.map((group, i) => (
        <div key={i} className="border border-gray-700 rounded-lg p-4">
          <h4 className="font-semibold mb-2 text-[#ff7f32] text-sm">Group {String(i + 1).padStart(4, '0')}</h4>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left p-2 w-24 text-xs">No Reg</th>
                  <th className="text-left p-2 text-xs">Athlet</th>
                  <th className="text-left p-2 text-xs">Gardu/Club</th>
                </tr>
              </thead>
              <tbody>
                {group.map((participant, idx) => (
                  <tr key={idx} className="border-b border-gray-700">
                    <td className="p-2 text-xs">{participant.noReg}</td>
                    <td className="p-2 text-xs">{participant.name}</td>
                    <td className="p-2 text-xs">{participant.club}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ))}
    </div>
  );

  // Helper untuk ambil userName dari localStorage/session jika ada
  function getUserName() {
    if (typeof window === 'undefined') return 'Unknown User';
    try {
      const session = localStorage.getItem('session');
      if (session) {
        const s = JSON.parse(session);
        if (s.user && s.user.name) return s.user.name;
      }
    } catch {}
    return 'Unknown User';
  }

  // Fungsi export PDF distribusi grup
  const exportGroupsToPDF = () => {
    const doc = new jsPDF();
    let y = 10;
    
    distributedGroups.forEach((group, groupIdx) => {
      const groupName = `Group ${String(groupIdx + 1).padStart(4, '0')}`;
      doc.setFontSize(12);
      doc.text(groupName, 10, y);
      y += 8;
      
      const data = group.map(p => [p.noReg, p.name, p.club || '-']);
      
      autoTable(doc, {
        head: [['No Reg', 'Nama', 'Gardu/Club']],
        body: data,
        startY: y,
        theme: 'grid',
        headStyles: { 
          fillColor: [204, 121, 0],
          textColor: 255,
          fontSize: 10
        },
        styles: { 
          fontSize: 9,
          cellPadding: 1.5,
          halign: 'left'
        },
        margin: { left: 10, right: 10 }
      });
      
      y = doc.lastAutoTable.finalY + 15;
      if (y > 260) {
        doc.addPage();
        y = 10;
      }
    });
    // Tambahkan info export di akhir file
    const now = new Date();
    const dateStr = now.toLocaleDateString('en-GB', { year: 'numeric', month: 'long', day: 'numeric' });
    const timeStr = now.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    const userName = getUserName();
    y += 10;
    doc.setFontSize(9);
    doc.text("EXPORT DATA INFORMATION:", 10, y);
    y += 7;
    doc.text(`Tournament Name: ${form.name || 'Unknown Tournament'}`, 10, y);
    y += 7;
    doc.text(`Status: ${form.status || '-'}`, 10, y);
    y += 7;
    doc.text(`Type: ${form.typeOfStage || '-'}`, 10, y);
    y += 7;
    doc.text(`Format: ${form.format || '-'}`, 10, y);
    y += 7;
    doc.text(`Group Stage Format: ${form.groupStageFormat || '-'}`, 10, y);
    y += 7;
    doc.text(`Final Stage Format: ${form.finalStageFormat || '-'}`, 10, y);
    y += 7;
    doc.text(`User Name: ${userName}`, 10, y);
    y += 7;
    doc.text(`Export Date: ${dateStr}`, 10, y);
    y += 7;
    doc.text(`Export Time: ${timeStr}`, 10, y);
    doc.setFontSize(8);
    doc.save('distribusi_grup.pdf');
    setPdfExported(true);
  };

  const handleCreateTournament = async () => {
    localStorage.setItem('tournamentGroups', JSON.stringify(distributedGroups));
    const res = await fetch("/api/tournament/create", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        ...form,
        status: "upcoming",
        participants: participants.length,
      }),
    });
    if (res.ok) {
      const data = await res.json();

      // Determine which page to redirect to based on tournament format
      const isSEDEFormat = (form.typeOfStage === 'single' && (form.format === 'single' || form.format === 'double')) ||
                          (form.typeOfStage === 'two' && (form.groupStageFormat === 'single' || form.groupStageFormat === 'double'));

      if (isSEDEFormat) {
        // Redirect to new Stage 1 SE/DE page
        window.location.href = `/tournaments/${data.tournament.id}/stage1`;
      } else {
        // Redirect to original bracket page for round-robin
        window.location.href = `/tournaments/${data.tournament.id}/bracket`;
      }
    } else {
      alert("Failed to create tournament");
    }
  };

  return (
    <div className="min-h-screen bg-[#333333] text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <div className="flex items-center gap-2 text-sm text-gray-400 mb-2">
              <Link href="/tournaments" className="hover:text-white">
                Tournaments
              </Link>
              <span>/</span>
              <span>Create Tournament</span>
            </div>
            <h1 className="text-3xl font-bold">Create Tournament</h1>
          </div>
        </div>
        <div className="grid md:grid-cols-[1fr_300px] gap-8">
          <div>
            <Card className="bg-[#2a2a2a] border-gray-700">
              <CardHeader>
                <CardTitle>Tournament Setup</CardTitle>
                <CardDescription>Configure your tournament settings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-8">
                  <div className="flex justify-between mb-2">
                    <div className="flex gap-2">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? "bg-[#ff7f32]" : "bg-gray-700"}`}>1</div>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? "bg-[#ff7f32]" : "bg-gray-700"}`}>2</div>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? "bg-[#ff7f32]" : "bg-gray-700"}`}>3</div>
                    </div>
                    <div className="text-sm text-gray-400">Step {step} of 3</div>
                  </div>
                  <div className="w-full bg-gray-700 h-2 rounded-full">
                    <div className="bg-[#ff7f32] h-2 rounded-full transition-all" style={{ width: `${(step / 3) * 100}%` }}></div>
                  </div>
                </div>
                {step === 1 && (
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <label htmlFor="tournament-name" className="text-sm font-medium text-white">Tournament Name</label>
                      <Input id="tournament-name" value={form.name} onChange={e => setForm(f => ({ ...f, name: e.target.value }))} placeholder="Enter tournament name" className="bg-[#333333] border-gray-700 text-white" />
                      {errors.name && <p className="text-red-400 text-xs">{errors.name}</p>}
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="status" className="text-sm font-medium text-white">Status</label>
                      <Select value={form.status} onValueChange={v => setForm(f => ({ ...f, status: v }))}>
                        <SelectTrigger className="bg-[#333333] border-gray-700 text-white">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#333333] border-gray-700 text-white">
                          <SelectItem value="Liga Domino Nasional">Liga Domino Nasional</SelectItem>
                          <SelectItem value="Kejuaraan Berjenjang">Kejuaraan Berjenjang</SelectItem>
                          <SelectItem value="Kejuaraan Terbuka">Kejuaraan Terbuka (Open Tournament)</SelectItem>
                          <SelectItem value="Kejuaraan Amatir">Kejuaraan Amatir</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.status && <p className="text-red-400 text-xs">{errors.status}</p>}
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="start-date" className="text-sm font-medium text-white">Start Date</label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className="w-full justify-start text-left font-normal bg-[#333333] border-gray-700 text-white" disabled={form.tentative}>
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {form.startDate ? format(form.startDate, "PPP") : <span>Pick a date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 bg-[#333333] border-gray-700">
                          <Calendar mode="single" selected={form.startDate} onSelect={d => setForm(f => ({ ...f, startDate: d || new Date() }))} initialFocus className="bg-[#333333]" />
                        </PopoverContent>
                      </Popover>
                      {errors.startDate && <p className="text-red-400 text-xs">{errors.startDate}</p>}
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="end-date" className="text-sm font-medium text-white">End Date</label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button variant="outline" className="w-full justify-start text-left font-normal bg-[#333333] border-gray-700 text-white" disabled={form.tentative}>
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {form.endDate ? format(form.endDate, "PPP") : <span>Pick a date</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 bg-[#333333] border-gray-700">
                          <Calendar mode="single" selected={form.endDate} onSelect={d => setForm(f => ({ ...f, endDate: d || new Date() }))} initialFocus className="bg-[#333333]" />
                        </PopoverContent>
                      </Popover>
                      {errors.endDate && <p className="text-red-400 text-xs">{errors.endDate}</p>}
                    </div>
                    <div className="flex items-center gap-2">
                      <input type="checkbox" id="tentative" checked={form.tentative} onChange={e => setForm(f => ({ ...f, tentative: e.target.checked }))} />
                      <label htmlFor="tentative" className="text-sm text-white">Mark as tentative</label>
                    </div>
                    <div className="pt-4 flex justify-end">
                      <Button onClick={() => { if (validateStep1()) nextStep() }} className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white">Next Step</Button>
                    </div>
                  </div>
                )}
                {step === 2 && (
                  <div className="space-y-6">
                    {/* Upload Excel Participants */}
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-white">Upload Participants (Excel)</label>
                      <div className="border-2 border-dashed border-gray-500 rounded-lg p-6 flex flex-col items-center justify-center bg-[#222]">
                        <input type="file" accept=".xlsx,.xls" className="mb-2" onChange={handleExcelUpload} />
                        <span className="text-xs text-gray-400">Drag & drop or click to select Excel file</span>
                      </div>
                      <Link href="/Daftar%20Nama%20Participant_tamplate.xlsx" download className="text-[#ff7f32] text-sm font-semibold hover:underline mt-2 inline-block">
                        Download participant template
                      </Link>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="num-participants" className="text-sm font-medium text-white">Number of Participants</label>
                      <Input id="num-participants" type="number" min={2} value={form.participants} onChange={e => setForm(f => ({ ...f, participants: Number(e.target.value) }))} className="bg-[#333333] border-gray-700 text-white" />
                      {errors.participants && <p className="text-red-400 text-xs">{errors.participants}</p>}
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium text-white">Type of Stage</label>
                      <div className="flex gap-6 items-center">
                        <label className="flex items-center gap-2">
                          <input type="radio" name="typeOfStage" value="single" checked={form.typeOfStage === "single"} onChange={() => setForm(f => ({ ...f, typeOfStage: "single" }))} />
                          <span>Single Stage Tournament</span>
                        </label>
                        <label className="flex items-center gap-2">
                          <input type="radio" name="typeOfStage" value="two" checked={form.typeOfStage === "two"} onChange={() => setForm(f => ({ ...f, typeOfStage: "two" }))} />
                          <span>Two Stage Tournament <span className="text-xs text-gray-400">— groups compete separately, winners proceed to a final stage</span></span>
                        </label>
                      </div>
                      {errors.typeOfStage && <p className="text-red-400 text-xs">{errors.typeOfStage}</p>}
                    </div>
                    {form.typeOfStage === "single" && (
                      <div className="space-y-2">
                        <label htmlFor="tournament-format" className="text-sm font-medium text-white">Tournament Format</label>
                        <Select value={form.format} onValueChange={v => setForm(f => ({ ...f, format: v }))}>
                          <SelectTrigger className="bg-[#333333] border-gray-700 text-white">
                            <SelectValue placeholder="Select format" />
                          </SelectTrigger>
                          <SelectContent className="bg-[#333333] border-gray-700 text-white">
                            <SelectItem value="single">Single Elimination</SelectItem>
                            <SelectItem value="double">Double Elimination</SelectItem>
                            <SelectItem value="round">Round Robin</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.format && <p className="text-red-400 text-xs">{errors.format}</p>}
                      </div>
                    )}
                    {form.typeOfStage === "two" && (
                      <>
                        <div className="space-y-2">
                          <label htmlFor="group-stage-format" className="text-sm font-medium text-white">Group Stage</label>
                          <Select value={form.groupStageFormat} onValueChange={v => setForm(f => ({ ...f, groupStageFormat: v }))}>
                            <SelectTrigger className="bg-[#333333] border-gray-700 text-white">
                              <SelectValue placeholder="Select group stage format" />
                            </SelectTrigger>
                            <SelectContent className="bg-[#333333] border-gray-700 text-white">
                              <SelectItem value="single">Single Elimination</SelectItem>
                              <SelectItem value="double">Double Elimination</SelectItem>
                              <SelectItem value="round">Round Robin</SelectItem>
                            </SelectContent>
                          </Select>
                          {errors.groupStageFormat && <p className="text-red-400 text-xs">{errors.groupStageFormat}</p>}
                        </div>
                        <div className="grid grid-cols-2 gap-4 items-start">
                          <div className="space-y-2">
                            <label htmlFor="group-participants" className="text-sm font-medium text-white">Participants per group</label>
                            <Input id="group-participants" type="text" value={form.groupParticipants || ""} readOnly className="bg-[#333333] border-gray-700 text-white" placeholder=" " />
                            {form.groupStageFormat && (
                              <div className="mt-2 flex items-center gap-2">
                                <span className="text-xs text-[#ff7f32] font-semibold">{rrGroupInfo.valid ? rrGroupInfo.text : 'Invalid participants'}</span>
                                {rrGroupInfo.valid && (
                                  <button type="button" onClick={() => {
                                    setGroupValidated(true);
                                    // Ambil semua angka peserta per grup dari rrGroupInfo.text
                                    const matches = [...rrGroupInfo.text.matchAll(/(\d+) groups? with (\d+) participants/g)];
                                    if (matches.length === 1) {
                                      setForm(f => ({ ...f, groupParticipants: matches[0][2] }));
                                    } else if (matches.length > 1) {
                                      // Gabungkan semua angka unik dengan format "4 - 5"
                                      const unique = [...new Set(matches.map(m => m[2]))];
                                      setForm(f => ({ ...f, groupParticipants: unique.join(' - ') }));
                                    }
                                  }} className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white px-2 py-0.5 rounded text-xs">OK</button>
                                )}
                                {!groupValidated && errors.groupValidated && (
                                  <p className="text-red-400 text-xs ml-2">{errors.groupValidated}</p>
                                )}
                              </div>
                            )}
                          </div>
                          <div className="space-y-2">
                            <label htmlFor="group-advance" className="text-sm font-medium text-white">Participants advance</label>
                            <Input id="group-advance" type="number" min={1} value={form.groupAdvance} onChange={e => setForm(f => ({ ...f, groupAdvance: Number(e.target.value) }))} className="bg-[#333333] border-gray-700 text-white" />
                            {errors.groupAdvance && <p className="text-red-400 text-xs">{errors.groupAdvance}</p>}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label htmlFor="final-stage-format" className="text-sm font-medium text-white">Final Stage</label>
                          <Select value={form.finalStageFormat} onValueChange={v => setForm(f => ({ ...f, finalStageFormat: v }))}>
                            <SelectTrigger className="bg-[#333333] border-gray-700 text-white">
                              <SelectValue placeholder="Select final stage format" />
                            </SelectTrigger>
                            <SelectContent className="bg-[#333333] border-gray-700 text-white">
                              {finalStageOptions.map(opt => (
                                <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          {errors.finalStageFormat && <p className="text-red-400 text-xs">{errors.finalStageFormat}</p>}
                        </div>
                      </>
                    )}
                    <div className="space-y-2">
                      <label htmlFor="match-format" className="text-sm font-medium text-white">Match Format</label>
                      <Select value={form.matchFormat} onValueChange={v => setForm(f => ({ ...f, matchFormat: v }))}>
                        <SelectTrigger className="bg-[#333333] border-gray-700 text-white">
                          <SelectValue placeholder="Select match format" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#333333] border-gray-700 text-white">
                          <SelectItem value="bo1">Best of 1</SelectItem>
                          <SelectItem value="bo3">Best of 3</SelectItem>
                          <SelectItem value="bo5">Best of 5</SelectItem>
                          <SelectItem value="bo7">Best of 7</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.matchFormat && <p className="text-red-400 text-xs">{errors.matchFormat}</p>}
                    </div>
                    <div className="pt-4 flex justify-between">
                      <Button onClick={prevStep} variant="outline" className="border-gray-700 hover:bg-gray-700">Previous</Button>
                      <Button onClick={() => { if (validateStep2()) nextStep() }} className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white">Next Step</Button>
                    </div>
                  </div>
                )}
                {step === 3 && (
                  <div className="space-y-6">
                    <h2 className="text-lg font-semibold mb-4">Participant Pairing</h2>
                    <div className="bg-[#333333] p-4 rounded-lg">
                      <div className="flex justify-between items-center mb-4">
                        <div>
                          <h3 className="font-bold text-base">{form.name}</h3>
                          <p className="text-xs text-gray-400">
                            {form.participants} Participants • {form.groupStageFormat === "round" ? "Round Robin" : form.groupStageFormat === "single" ? "Single Elimination" : "Double Elimination"} Groups
                          </p>
                        </div>
                        <Button 
                          onClick={handleDistributeOrShuffle}
                          className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white text-sm"
                        >
                          re-Shuffle
                        </Button>
                      </div>

                      {/* Group Distribution Table */}
                      <div className="space-y-4">
                        {form.typeOfStage === "two" && form.groupStageFormat && (
                          participants.length > 0 ? (
                            <div className="mb-8">
                              {renderDistributedGroups()}
                      <div className="flex justify-end gap-2 mt-10">
                        <button onClick={exportGroupsToPDF} className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white px-3 py-1 rounded text-xs">Export to PDF</button>
                      </div>
                            </div>
                          ) : (
                            <div className="col-span-2 text-center py-8">
                              <p className="text-xs text-gray-400">Please upload participant data first</p>
                            </div>
                          )
                        )}

                        {form.typeOfStage === "single" && (
                          <div className="text-center py-8">
                            <p className="text-xs text-gray-400">Single stage tournament - no group distribution needed</p>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="pt-4 flex justify-between">
                      <Button onClick={prevStep} variant="outline" className="border-gray-700 hover:bg-gray-700 text-sm">Previous</Button>
                      <Button
                        onClick={() => {
                          if (pdfExported) {
                            handleCreateTournament();
                          } else {
                            setIsDialogOpen(true);
                          }
                        }}
                        className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white text-sm"
                      >
                        Create Tournament
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          <div>
            <Card className="bg-[#2a2a2a] border-gray-700">
              <CardHeader>
                <CardTitle>Tournament Preview</CardTitle>
                <CardDescription>How your tournament will appear</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-[#333333] p-4 rounded-lg">
                    <h3 className="font-bold text-lg mb-1">{form.name || "Your Tournament Name"}</h3>
                    <div className="text-sm text-gray-400 mb-2">
                      <span className="inline-block bg-[#ff7f32] text-white text-xs px-2 py-0.5 rounded mr-2">{form.format ? (form.format === "single" ? "Single Elimination" : form.format === "double" ? "Double Elimination" : form.format === "round" ? "Round Robin" : "Group Stage + Playoffs") : "Format"}</span>
                      <span>{form.participants} Participants</span>
                    </div>
                    <div className="text-sm text-gray-400">Start: {form.startDate ? format(form.startDate, "PPP") : "Date"}</div>
                    <div className="text-sm text-gray-400">End: {form.endDate ? format(form.endDate, "PPP") : "Date"}</div>
                  </div>
                  <div className="text-sm text-gray-400">
                    <p className="mb-4">This preview shows how your tournament will appear in listings. Complete all steps to create your tournament.</p>
                    <p>After creation, you'll be able to:</p>
                    <ul className="list-disc pl-5 space-y-1 mt-2">
                      <li>Invite participants</li>
                      <li>Customize bracket settings</li>
                      <li>Add tournament rules</li>
                      <li>Schedule matches</li>
                      <li>Manage results</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <AlertDialogContent className="bg-[#2a2a2a] border-gray-700 text-white">
          <AlertDialogHeader>
            <AlertDialogTitle>Export Required</AlertDialogTitle>
            <AlertDialogDescription className="text-gray-400">
              You must export the group distribution to a PDF before creating the tournament. Do you want to export the PDF and create the tournament now?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-600 hover:bg-gray-700 border-0">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                exportGroupsToPDF();
                handleCreateTournament();
              }}
              className="bg-[#ff7f32] hover:bg-[#e86a1f]"
            >
              Yes, Export and Create
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

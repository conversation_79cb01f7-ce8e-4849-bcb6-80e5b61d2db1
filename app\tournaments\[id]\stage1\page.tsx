"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { DynamicSEBracket } from "@/components/DynamicSEBracket";
import { BracketConfigurationBuilder } from "@/utils/bracketConfiguration";
import { GroupManager, Group, GroupResult } from "@/utils/groupManagement";
import "@/styles/dynamicBracket.css";

interface TournamentSettings {
  id: string;
  name: string;
  status: string;
  typeOfStage: string;
  groupStageFormat: string;
  groupAdvance: number;
  finalStageFormat: string;
  matchFormat: string;
  maxTeams: number;
  startDate: string;
  endDate: string;
}

interface Participant {
  noReg: string;
  name: string;
  club: string;
}

export default function Stage1Page() {
  const params = useParams();
  const router = useRouter();
  const tournamentId = params.id as string;
  
  const [tournament, setTournament] = useState<TournamentSettings | null>(null);
  const [groups, setGroups] = useState<Group[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [currentStep, setCurrentStep] = useState(1); // 1: Groups List, 2: Bracket Display
  const [activeGroupIndex, setActiveGroupIndex] = useState(0);
  const [groupManager, setGroupManager] = useState<GroupManager | null>(null);

  // Fetch tournament settings
  useEffect(() => {
    const fetchTournamentSettings = async () => {
      try {
        const response = await fetch(`/api/tournament/${tournamentId}/settings`);
        if (response.ok) {
          const data = await response.json();
          setTournament(data.tournament);
        } else {
          setError("Failed to fetch tournament settings");
        }
      } catch (err) {
        setError("Error fetching tournament settings");
        console.error(err);
      }
    };

    fetchTournamentSettings();
  }, [tournamentId]);

  // Initialize GroupManager and load groups
  useEffect(() => {
    if (tournament) {
      try {
        const manager = new GroupManager(tournamentId);
        setGroupManager(manager);

        const loadedGroups = manager.getGroups();
        if (loadedGroups.length > 0) {
          setGroups(loadedGroups);
        } else {
          // Try to load from legacy format and convert
          const legacyData = localStorage.getItem('tournamentGroups');
          if (legacyData) {
            const parsedGroups = JSON.parse(legacyData);
            const participants: Participant[] = parsedGroups.flat();

            // Create SE/DE groups using GroupManager
            const format = tournament.groupStageFormat === 'double' ? 'double' : 'single';
            const advanceCount = tournament.groupAdvance || 2;

            const newGroups = manager.createSEDEGroups(participants, format, advanceCount);
            setGroups(newGroups);
          }
        }

        setLoading(false);
      } catch (err) {
        setError("Error initializing group management");
        setLoading(false);
        console.error(err);
      }
    }
  }, [tournament, tournamentId]);

  // Handle match score updates
  const handleMatchUpdate = (groupIndex: number, matchId: string, score1: number, score2: number) => {
    console.log(`Group ${groupIndex + 1} - Match ${matchId} updated: ${score1} - ${score2}`);
    // Here you can add logic to save match results to database or localStorage
    
    // Save to localStorage with tournament-specific key
    const storageKey = `stage1_matches_${tournamentId}_group_${groupIndex}`;
    const existingMatches = JSON.parse(localStorage.getItem(storageKey) || '{}');
    existingMatches[matchId] = { score1, score2, timestamp: Date.now() };
    localStorage.setItem(storageKey, JSON.stringify(existingMatches));
  };

  // Get bracket configuration based on tournament settings
  const getBracketConfig = (participantCount: number) => {
    const eliminationType = tournament?.groupStageFormat === 'double' ? 'double' : 'single';
    
    return new BracketConfigurationBuilder()
      .setParticipants(participantCount)
      .setEliminationType(eliminationType)
      .setTheme('dark')
      .setSpacing('normal')
      .setSizing('medium')
      .setAnimation(true, 500)
      .build();
  };

  // Check if tournament is SE/DE format
  const isSEDEFormat = () => {
    if (tournament?.typeOfStage === 'single') {
      return tournament.type === 'single elimination' || tournament.type === 'double elimination';
    } else if (tournament?.typeOfStage === 'two') {
      return tournament.groupStageFormat === 'single' || tournament.groupStageFormat === 'double';
    }
    return false;
  };

  // Handle proceed to next stage
  const handleProceedToNextStage = () => {
    if (!groupManager) {
      setError("Group manager not initialized");
      return;
    }

    if (tournament?.typeOfStage === 'single') {
      // Single stage tournament - this is the final stage
      alert('Tournament completed! This was a single stage tournament.');
    } else {
      // Two stage tournament - proceed to final stage
      // Get winners from GroupManager
      const groupWinners = groupManager.getGroupWinners();

      if (groupWinners.length === 0) {
        alert('No group results found. Please complete all group brackets first.');
        return;
      }

      // Convert to format expected by final stage
      const groupResults: { [groupName: string]: any[] } = {};

      groups.forEach((group) => {
        const groupWinnersForThisGroup = groupWinners.filter(winner =>
          group.participants.some(p => p.noReg === winner.participant.noReg)
        );

        groupResults[group.groupName] = groupWinnersForThisGroup.map(winner => ({
          noReg: winner.participant.noReg,
          name: winner.participant.name,
          club: winner.participant.club,
          points: winner.points,
          wins: winner.wins,
          losses: winner.losses,
          rank: winner.rank
        }));
      });

      localStorage.setItem(`standings_${tournamentId}`, JSON.stringify(groupResults));

      // Update tournament status and navigate to final stage
      fetch(`/api/tournament/${tournamentId}/finalstage`, {
        method: "PATCH",
      }).then(res => {
        if (res.ok) {
          window.location.href = `/tournaments/${tournamentId}/final`;
        }
      }).catch(err => {
        console.error("Failed to update tournament status:", err);
        window.location.href = `/tournaments/${tournamentId}/final`;
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#1a1a1a] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#ff7f32] mx-auto mb-4"></div>
          <p>Loading Stage 1...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#1a1a1a] text-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="bg-[#ff7f32] hover:bg-[#e86a1f] px-4 py-2 rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // If not SE/DE format, redirect to original bracket page
  if (!isSEDEFormat()) {
    window.location.href = `/tournaments/${tournamentId}/bracket`;
    return null;
  }

  return (
    <div className="min-h-screen bg-[#333333] text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Stage 1 - {tournament?.groupStageFormat?.toUpperCase()} Elimination</h1>
          <h2 className="text-xl text-gray-300">{tournament?.name}</h2>
          <div className="flex gap-4 mt-4 text-sm">
            <span className="bg-[#2a2a2a] px-3 py-1 rounded">
              Format: {tournament?.groupStageFormat?.toUpperCase()}
            </span>
            <span className="bg-[#2a2a2a] px-3 py-1 rounded">
              Type: {tournament?.typeOfStage === 'single' ? 'Single Stage' : 'Two Stage'}
            </span>
            <span className="bg-[#2a2a2a] px-3 py-1 rounded">
              Groups: {groups.length}
            </span>
          </div>
        </div>

        {/* Step Indicator */}
        <div className="flex items-center justify-center mb-8">
          {[1, 2].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                currentStep >= step ? 'bg-[#ff7f32] text-white' : 'bg-gray-600 text-gray-300'
              }`}>
                {step}
              </div>
              {step < 2 && (
                <div className={`w-16 h-1 ${
                  currentStep > step ? 'bg-[#ff7f32]' : 'bg-gray-600'
                }`}></div>
              )}
            </div>
          ))}
        </div>

        {/* Step 1: Groups Overview */}
        {currentStep === 1 && (
          <div className="bg-[#2a2a2a] rounded-lg p-6">
            <h3 className="text-xl font-bold mb-4">Step 1: Groups Overview</h3>
            <p className="text-gray-300 mb-6">
              Berikut adalah pembagian grup untuk Stage 1 dengan format {tournament?.groupStageFormat?.toUpperCase()} Elimination:
            </p>

            <div className="grid gap-4 mb-6">
              {groups.map((group, index) => (
                <div key={index} className="bg-[#333333] rounded-lg p-4">
                  <h4 className="font-bold text-[#ff7f32] mb-3">{group.groupName}</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {group.participants.map((participant, pIndex) => (
                      <div key={pIndex} className="bg-[#404040] p-2 rounded text-sm">
                        <p className="font-semibold">{participant.noReg} ] {participant.name}</p>
                      </div>
                    ))}
                  </div>
                  <div className="mt-3 text-sm text-gray-400">
                    {group.participants.length} participants • {tournament?.groupStageFormat?.toUpperCase()} Elimination
                  </div>
                </div>
              ))}
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => router.push(`/tournaments/${tournamentId}`)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded"
              >
                Back
              </button>
              <button
                onClick={() => setCurrentStep(2)}
                className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white px-6 py-2 rounded font-semibold"
                disabled={groups.length === 0}
              >
                Start Group Brackets
              </button>
            </div>
          </div>
        )}

        {/* Step 2: Group Brackets */}
        {currentStep === 2 && (
          <div className="bg-[#2a2a2a] rounded-lg p-6">
            <h3 className="text-xl font-bold mb-4">Step 2: Group Brackets</h3>
            
            {/* Group Selector */}
            <div className="mb-6">
              <div className="flex flex-wrap gap-2">
                {groups.map((group, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveGroupIndex(index)}
                    className={`px-4 py-2 rounded font-semibold ${
                      activeGroupIndex === index
                        ? 'bg-[#ff7f32] text-white'
                        : 'bg-[#404040] text-gray-300 hover:bg-[#505050]'
                    }`}
                  >
                    {group.groupName}
                  </button>
                ))}
              </div>
            </div>

            {/* Active Group Bracket */}
            {groups[activeGroupIndex] && (
              <div className="mb-6">
                <h4 className="text-lg font-bold mb-4 text-[#ff7f32]">
                  {groups[activeGroupIndex].groupName} - {tournament?.groupStageFormat?.toUpperCase()} Elimination
                </h4>
                
                <DynamicSEBracket
                  participants={groups[activeGroupIndex].participants.map(p =>
                    `${p.noReg} ] ${p.name}`
                  )}
                  seedingMethod="random"
                  customConfig={getBracketConfig(groups[activeGroupIndex].participants.length)}
                  onMatchUpdate={(matchId, score1, score2) =>
                    handleMatchUpdate(activeGroupIndex, matchId, score1, score2)
                  }
                  className="bg-[#333333] rounded-lg"
                />
              </div>
            )}

            <div className="flex justify-between">
              <div className="flex gap-2">
                <button
                  onClick={() => router.push(`/tournaments/${tournamentId}`)}
                  className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded"
                >
                  Back
                </button>
                <button
                  onClick={() => setCurrentStep(1)}
                  className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded"
                >
                  Previous Step
                </button>
              </div>
              <button
                onClick={handleProceedToNextStage}
                className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded font-semibold"
              >
                {tournament?.typeOfStage === 'single' ? '🏆 Complete Tournament' : '🚀 Proceed to Final Stage'}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

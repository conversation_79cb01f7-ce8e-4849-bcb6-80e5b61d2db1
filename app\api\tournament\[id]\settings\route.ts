import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  const { id } = params;
  
  try {
    const tournament = await prisma.tournament.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        status: true,
        typeOfStage: true,
        groupStageFormat: true,
        groupAdvance: true,
        finalStageFormat: true,
        matchFormat: true,
        maxTeams: true,
        startDate: true,
        endDate: true,
      },
    });

    if (!tournament) {
      return NextResponse.json({ error: "Tournament not found" }, { status: 404 });
    }

    return NextResponse.json({ tournament });
  } catch (err) {
    console.error("Error fetching tournament settings:", err);
    return NextResponse.json({ error: "Failed to fetch tournament settings" }, { status: 500 });
  }
}

"use client";
import { useSession } from "next-auth/react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export default function EditProfilePage() {
  const { data: session, update } = useSession();
  const [name, setName] = useState(session?.user?.name || "");
  const [email] = useState(session?.user?.email || "");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState("");
  const [error, setError] = useState("");
  const [fullName, setFullName] = useState(session?.user?.fullName || "");
  const [phone, setPhone] = useState(session?.user?.phone || "");
  const [bio, setBio] = useState(session?.user?.bio || "");

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess("");
    try {
      const res = await fetch("/api/user/update-profile", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, fullName, phone, bio }),
      });
      if (!res.ok) throw new Error("Failed to update profile");
      setSuccess("Profile updated successfully!");
      update(); // refresh session
    } catch (err: any) {
      setError(err.message || "Failed to update profile");
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="min-h-screen bg-[#333333] text-white flex flex-col items-center justify-center">
      <div className="bg-[#2a2a2a] p-8 rounded-lg shadow-md w-full max-w-md">
        <h1 className="text-2xl font-bold mb-6">Edit Profile</h1>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block mb-1 text-sm font-medium">Username</label>
            <Input
              type="text"
              value={name}
              onChange={e => setName(e.target.value)}
              required
              className="bg-[#333333] border-gray-700 text-white"
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-medium">Full Name</label>
            <Input
              type="text"
              value={fullName}
              onChange={e => setFullName(e.target.value)}
              className="bg-[#333333] border-gray-700 text-white"
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-medium">Phone</label>
            <Input
              type="text"
              value={phone}
              onChange={e => setPhone(e.target.value)}
              className="bg-[#333333] border-gray-700 text-white"
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-medium">Bio</label>
            <textarea
              value={bio}
              onChange={e => setBio(e.target.value)}
              className="bg-[#333333] border-gray-700 text-white w-full rounded p-2"
              rows={3}
            />
          </div>
          <div>
            <label className="block mb-1 text-sm font-medium">Email</label>
            <Input
              type="email"
              value={email}
              readOnly
              className="bg-[#333333] border-gray-700 text-white opacity-60 cursor-not-allowed"
            />
          </div>
          {success && <div className="text-green-400 text-sm">{success}</div>}
          {error && <div className="text-red-400 text-sm">{error}</div>}
          <Button type="submit" className="w-full bg-[#ff7f32] hover:bg-[#e86a1f] text-white" disabled={loading}>
            {loading ? "Saving..." : "Save"}
          </Button>
        </form>
      </div>
    </div>
  );
} 
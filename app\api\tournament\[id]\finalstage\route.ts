import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  const { id } = params;
  try {
    const tournament = await prisma.tournament.update({
      where: { id },
      data: { status: "finalstage" },
    });
    // Dummy: tidak generate bracket, hanya update status
    return NextResponse.json({ success: true, tournament });
  } catch (err) {
    return NextResponse.json({ error: "Failed to proceed to final stage" }, { status: 500 });
  }
} 
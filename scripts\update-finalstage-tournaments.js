const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateFinalstageToCompleted() {
  try {
    console.log('🔄 Updating tournaments with status "finalstage" to "completed"...\n');
    
    // Find all tournaments with finalstage status
    const finalstageTorunaments = await prisma.tournament.findMany({
      where: { status: 'finalstage' },
      select: {
        id: true,
        name: true,
        status: true,
      }
    });

    if (finalstageTorunaments.length === 0) {
      console.log('✅ No tournaments with "finalstage" status found.');
      return;
    }

    console.log(`📊 Found ${finalstageTorunaments.length} tournaments with "finalstage" status:`);
    finalstageTorunaments.forEach((t, index) => {
      console.log(`${index + 1}. ${t.name} (ID: ${t.id})`);
    });
    console.log('');

    // Update all finalstage tournaments to completed
    const updateResult = await prisma.tournament.updateMany({
      where: { status: 'finalstage' },
      data: { status: 'completed' }
    });

    console.log(`✅ Successfully updated ${updateResult.count} tournaments to "completed" status.`);
    
    // Verify the update
    const completedTournaments = await prisma.tournament.findMany({
      where: { status: 'completed' },
      select: {
        id: true,
        name: true,
        status: true,
      }
    });

    console.log(`\n📈 Now there are ${completedTournaments.length} tournaments with "completed" status:`);
    completedTournaments.forEach((t, index) => {
      console.log(`${index + 1}. ${t.name}`);
    });

  } catch (error) {
    console.error('❌ Error updating tournaments:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateFinalstageToCompleted();

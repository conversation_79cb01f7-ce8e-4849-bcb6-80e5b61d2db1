"use client";
import Image from "next/image";
import { useSession } from "next-auth/react";

export default function SidebarUser() {
  const { data: session } = useSession();
  const userName = session?.user?.name || "PANGLIMA DOMINO";
  return (
    <div className="flex flex-col items-center">
      <Image src="/PENGDA JAKSEL.png" alt="Organization Logo" width={120} height={120} className="rounded-full mb-6" />
      <div className="mb-8 text-2xl font-bold tracking-wide text-center">
        {userName}
      </div>
    </div>
  );
} 
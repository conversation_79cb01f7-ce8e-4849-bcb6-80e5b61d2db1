const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkTournaments() {
  try {
    console.log('🔍 Checking all tournaments in database...\n');
    
    const tournaments = await prisma.tournament.findMany({
      select: {
        id: true,
        name: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: { createdAt: 'desc' }
    });

    if (tournaments.length === 0) {
      console.log('❌ No tournaments found in database.');
      return;
    }

    console.log(`📊 Found ${tournaments.length} tournaments:\n`);
    
    tournaments.forEach((tournament, index) => {
      console.log(`${index + 1}. ${tournament.name}`);
      console.log(`   ID: ${tournament.id}`);
      console.log(`   Status: ${tournament.status}`);
      console.log(`   Created: ${tournament.createdAt.toISOString()}`);
      console.log(`   Updated: ${tournament.updatedAt.toISOString()}`);
      console.log('');
    });

    // Count by status
    const statusCounts = tournaments.reduce((acc, t) => {
      acc[t.status] = (acc[t.status] || 0) + 1;
      return acc;
    }, {});

    console.log('📈 Status Summary:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`   ${status}: ${count}`);
    });

  } catch (error) {
    console.error('❌ Error checking tournaments:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkTournaments();

/**
 * Dynamic Tournament Bracket CSS
 * Responsive patterns for all bracket sizes
 * Created by: Panglima's Dynamic Bracket System
 */

/* Base Bracket Container */
.dynamic-tournament-bracket {
  @apply w-full overflow-x-auto overflow-y-hidden;
  min-height: 600px;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  border-radius: 12px;
  padding: 24px;
  position: relative;
}

/* Responsive Grid System */
.dynamic-tournament-bracket .bracket-grid {
  display: grid;
  gap: clamp(12px, 4vw, 64px);
  grid-auto-flow: column;
  align-items: start;
  min-width: fit-content;
}

/* Grid Columns for Different Bracket Sizes */
.bracket-grid.rounds-2 { grid-template-columns: 1fr 1fr; }
.bracket-grid.rounds-3 { grid-template-columns: 1fr 1fr 1fr; }
.bracket-grid.rounds-4 { grid-template-columns: 1fr 1fr 1fr 1fr; }
.bracket-grid.rounds-5 { grid-template-columns: 1fr 1fr 1fr 1fr 1fr; }
.bracket-grid.rounds-6 { grid-template-columns: 1fr 1fr 1fr 1fr 1fr 1fr; }

/* Round Headers */
.bracket-round-header {
  @apply text-center mb-6;
}

.bracket-round-header h5 {
  @apply font-semibold text-white text-sm py-2 px-4 bg-gradient-to-r from-[#404040] to-[#505050] rounded-lg shadow-lg;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.bracket-round-header .match-count {
  @apply text-xs text-gray-400 mt-2 font-medium;
}

/* Match Container */
.bracket-match {
  @apply bg-gradient-to-br from-[#404040] to-[#353535] rounded-lg border border-gray-600 relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 80px;
}

.bracket-match:hover {
  @apply border-[#ff7f32] shadow-lg;
  box-shadow: 0 6px 20px rgba(255, 127, 50, 0.2);
  transform: translateY(-2px);
}

/* Match Number Badge */
.bracket-match-number {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-sm font-bold z-10;
  background: linear-gradient(135deg, #ff7f32, #ff9f52);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  box-shadow: 0 2px 6px rgba(255, 127, 50, 0.3);
}

/* Participants Container */
.bracket-participants {
  @apply divide-y divide-gray-600 ml-10;
}

.bracket-participant {
  @apply flex items-center justify-between p-3 transition-colors duration-200;
}

.bracket-participant:hover {
  @apply bg-black bg-opacity-20;
}

.bracket-participant.winner {
  @apply bg-gradient-to-r from-green-900 to-green-800 bg-opacity-30;
}

.bracket-participant.loser {
  @apply opacity-60;
}

/* Participant Info */
.participant-info {
  @apply flex-1 min-w-0;
}

.participant-name {
  @apply text-sm font-medium text-white truncate;
  max-width: 180px;
}

.participant-name.bye {
  @apply text-gray-500 italic;
}

.participant-name.tbd {
  @apply text-gray-400 italic;
}

/* Score Input */
.score-input {
  @apply w-12 h-7 ml-3 rounded-md bg-[#2a2a2a] text-center text-sm border border-gray-600 text-white;
  transition: all 0.2s ease;
}

.score-input:focus {
  @apply border-[#ff7f32] outline-none ring-2 ring-[#ff7f32] ring-opacity-30;
}

.score-input:disabled {
  @apply bg-gray-700 text-gray-500 cursor-not-allowed;
}

/* Connection Lines */
.bracket-connection {
  @apply absolute top-1/2 transform -translate-y-1/2 bg-gray-400;
  height: 2px;
  z-index: 5;
  transition: background-color 0.3s ease;
}

.bracket-connection.active {
  @apply bg-[#ff7f32];
  box-shadow: 0 0 8px rgba(255, 127, 50, 0.5);
}

.bracket-connection.horizontal {
  width: 32px;
  right: -32px;
}

.bracket-connection.vertical {
  width: 2px;
  height: var(--connection-height);
  right: -16px;
  top: var(--connection-top);
}

/* Responsive Breakpoints */
@media (max-width: 1536px) {
  .dynamic-tournament-bracket {
    padding: 20px;
  }
  
  .bracket-grid {
    gap: clamp(8px, 3vw, 48px);
  }
  
  .participant-name {
    max-width: 150px;
  }
}

@media (max-width: 1280px) {
  .dynamic-tournament-bracket {
    padding: 16px;
  }
  
  .bracket-grid {
    gap: clamp(6px, 2.5vw, 32px);
  }
  
  .participant-name {
    max-width: 120px;
  }
  
  .bracket-match-number {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}

@media (max-width: 1024px) {
  .dynamic-tournament-bracket {
    padding: 12px;
  }
  
  .bracket-grid {
    gap: clamp(4px, 2vw, 24px);
  }
  
  .participant-name {
    max-width: 100px;
    font-size: 12px;
  }
  
  .bracket-participant {
    padding: 8px 12px;
  }
  
  .score-input {
    width: 10px;
    height: 6px;
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .dynamic-tournament-bracket {
    padding: 8px;
    min-height: 400px;
  }
  
  .bracket-grid {
    gap: clamp(2px, 1.5vw, 16px);
  }
  
  .participant-name {
    max-width: 80px;
    font-size: 11px;
  }
  
  .bracket-round-header h5 {
    font-size: 11px;
    padding: 6px 8px;
  }
  
  .bracket-match {
    min-height: 60px;
  }
  
  .bracket-participants {
    margin-left: 28px;
  }
}

/* Animation Classes */
.bracket-match.animate-in {
  animation: slideInUp 0.5s ease-out;
}

.bracket-connection.animate-in {
  animation: drawLine 0.8s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes drawLine {
  from {
    width: 0;
  }
  to {
    width: 32px;
  }
}

/* Bracket Size Specific Styles */
.bracket-size-4 .bracket-match { min-height: 100px; }
.bracket-size-8 .bracket-match { min-height: 80px; }
.bracket-size-16 .bracket-match { min-height: 70px; }
.bracket-size-32 .bracket-match { min-height: 60px; }
.bracket-size-64 .bracket-match { min-height: 50px; }

/* Vertical Spacing Patterns */
.bracket-spacing-tight { --vertical-spacing: 8px; }
.bracket-spacing-normal { --vertical-spacing: 16px; }
.bracket-spacing-loose { --vertical-spacing: 32px; }
.bracket-spacing-extra-loose { --vertical-spacing: 48px; }

/* Theme Variants */
.bracket-theme-dark {
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
}

.bracket-theme-blue {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
}

.bracket-theme-green {
  background: linear-gradient(135deg, #166534 0%, #22c55e 100%);
}

/* Bracket Connection Lines */
.bracket-connection-line {
  transition: stroke 0.3s ease;
}

.bracket-connection-line:hover {
  stroke: #ff7f32;
  stroke-width: 3;
}

/* Connection Line Animations */
@keyframes connectionDraw {
  from {
    stroke-dasharray: 100;
    stroke-dashoffset: 100;
  }
  to {
    stroke-dasharray: 100;
    stroke-dashoffset: 0;
  }
}

.bracket-connection-animated {
  animation: connectionDraw 0.8s ease-out;
}

/* Enhanced Connection Styling */
.bracket-connector {
  position: absolute;
  pointer-events: none;
  z-index: 0;
}

.bracket-connector-horizontal {
  background: linear-gradient(90deg, #6b7280, #9ca3af);
  height: 2px;
  border-radius: 1px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.bracket-connector-vertical {
  background: linear-gradient(180deg, #6b7280, #9ca3af);
  width: 2px;
  border-radius: 1px;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.2);
}

/* Hover Effects for Connections */
.bracket-match:hover ~ .bracket-connector .bracket-connector-horizontal,
.bracket-match:hover ~ .bracket-connector .bracket-connector-vertical {
  background: linear-gradient(90deg, #ff7f32, #ff9f52);
  box-shadow: 0 0 8px rgba(255, 127, 50, 0.4);
}

/* Print Styles */
@media print {
  .dynamic-tournament-bracket {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .bracket-match {
    background: white !important;
    border: 2px solid #333 !important;
  }

  .participant-name {
    color: black !important;
  }

  .score-input {
    border: 1px solid #333 !important;
    background: white !important;
  }

  .bracket-connector-horizontal,
  .bracket-connector-vertical {
    background: #333 !important;
    box-shadow: none !important;
  }
}

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  const { id } = params;
  
  try {
    const tournament = await prisma.tournament.update({
      where: { id },
      data: { status: "completed" },
    });
    
    return NextResponse.json({ success: true, tournament });
  } catch (err) {
    console.error("Error completing tournament:", err);
    return NextResponse.json({ error: "Failed to complete tournament" }, { status: 500 });
  }
}

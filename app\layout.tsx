import type React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import ProtectedLayout from "@/components/ProtectedLayout";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Tournament Platform - Simplified Tournament Management",
  description: "Manage tournaments, host events, and keep competitive communities organized.",
  generator: "v0.dev",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <ProtectedLayout>
            {children}
          </ProtectedLayout>
        </Providers>
      </body>
    </html>
  );
}
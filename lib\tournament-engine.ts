export type TournamentFormat = "single-elimination" | "double-elimination" | "round-robin" | "swiss"

export interface Player {
  id: string
  name: string
  seed?: number
  rating?: number
}

export interface Match {
  id: string
  round: number
  position: number
  player1: Player | null
  player2: Player | null
  winner: Player | null
  score1: number
  score2: number
  status: "pending" | "in-progress" | "completed"
  nextMatchId?: string
  isLosersBracket?: boolean
}

export interface Tournament {
  id: string
  name: string
  format: TournamentFormat
  players: Player[]
  matches: Match[]
  status: "setup" | "in-progress" | "completed"
  currentRound: number
  maxRounds: number
}

export class TournamentEngine {
  private tournament: Tournament

  constructor(tournament: Tournament) {
    this.tournament = tournament
  }

  generateBracket(): Match[] {
    switch (this.tournament.format) {
      case "single-elimination":
        return this.generateSingleElimination()
      case "double-elimination":
        return this.generateDoubleElimination()
      case "round-robin":
        return this.generateRoundRobin()
      case "swiss":
        return this.generateSwiss()
      default:
        throw new Error("Unsupported tournament format")
    }
  }

  private generateSingleElimination(): Match[] {
    const players = [...this.tournament.players]
    const matches: Match[] = []

    // Ensure power of 2 participants
    const nextPowerOf2 = Math.pow(2, Math.ceil(Math.log2(players.length)))
    while (players.length < nextPowerOf2) {
      players.push({ id: `bye-${players.length}`, name: "BYE" })
    }

    let currentRound = 1
    let currentMatches = players.length / 2
    let matchId = 1

    // Generate first round
    for (let i = 0; i < players.length; i += 2) {
      matches.push({
        id: `match-${matchId}`,
        round: currentRound,
        position: Math.floor(i / 2) + 1,
        player1: players[i],
        player2: players[i + 1],
        winner: null,
        score1: 0,
        score2: 0,
        status: "pending",
      })
      matchId++
    }

    // Generate subsequent rounds
    while (currentMatches > 1) {
      currentRound++
      currentMatches = currentMatches / 2

      for (let i = 0; i < currentMatches; i++) {
        matches.push({
          id: `match-${matchId}`,
          round: currentRound,
          position: i + 1,
          player1: null,
          player2: null,
          winner: null,
          score1: 0,
          score2: 0,
          status: "pending",
        })
        matchId++
      }
    }

    return matches
  }

  private generateDoubleElimination(): Match[] {
    const winnersBracket = this.generateSingleElimination()
    const losersBracket: Match[] = []

    // Generate losers bracket structure
    // This is a simplified version - full implementation would be more complex
    const losersRounds = Math.ceil(Math.log2(this.tournament.players.length)) * 2 - 1

    for (let round = 1; round <= losersRounds; round++) {
      const matchesInRound = Math.ceil(this.tournament.players.length / Math.pow(2, round + 1))

      for (let i = 0; i < matchesInRound; i++) {
        losersBracket.push({
          id: `losers-${round}-${i + 1}`,
          round: round + 100, // Offset to distinguish from winners bracket
          position: i + 1,
          player1: null,
          player2: null,
          winner: null,
          score1: 0,
          score2: 0,
          status: "pending",
          isLosersBracket: true,
        })
      }
    }

    return [...winnersBracket, ...losersBracket]
  }

  private generateRoundRobin(): Match[] {
    const players = this.tournament.players
    const matches: Match[] = []
    let matchId = 1

    for (let i = 0; i < players.length; i++) {
      for (let j = i + 1; j < players.length; j++) {
        matches.push({
          id: `match-${matchId}`,
          round: 1, // All matches are in round 1 for round robin
          position: matchId,
          player1: players[i],
          player2: players[j],
          winner: null,
          score1: 0,
          score2: 0,
          status: "pending",
        })
        matchId++
      }
    }

    return matches
  }

  private generateSwiss(): Match[] {
    // Swiss system implementation would be more complex
    // This is a simplified version for the first round
    const players = [...this.tournament.players]
    const matches: Match[] = []

    // Shuffle players for first round
    for (let i = players.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[players[i], players[j]] = [players[j], players[i]]
    }

    for (let i = 0; i < players.length; i += 2) {
      if (i + 1 < players.length) {
        matches.push({
          id: `swiss-1-${Math.floor(i / 2) + 1}`,
          round: 1,
          position: Math.floor(i / 2) + 1,
          player1: players[i],
          player2: players[i + 1],
          winner: null,
          score1: 0,
          score2: 0,
          status: "pending",
        })
      }
    }

    return matches
  }

  updateMatchResult(matchId: string, winner: Player, score1: number, score2: number): void {
    const match = this.tournament.matches.find((m) => m.id === matchId)
    if (!match) throw new Error("Match not found")

    match.winner = winner
    match.score1 = score1
    match.score2 = score2
    match.status = "completed"

    // Advance winner to next round if applicable
    this.advanceWinner(match)
  }

  private advanceWinner(match: Match): void {
    if (!match.winner || !match.nextMatchId) return

    const nextMatch = this.tournament.matches.find((m) => m.id === match.nextMatchId)
    if (!nextMatch) return

    if (!nextMatch.player1) {
      nextMatch.player1 = match.winner
    } else if (!nextMatch.player2) {
      nextMatch.player2 = match.winner
    }
  }

  getStandings(): Player[] {
    // Calculate standings based on tournament format
    const standings = this.tournament.players.map((player) => ({
      ...player,
      wins: 0,
      losses: 0,
      points: 0,
    }))

    this.tournament.matches.forEach((match) => {
      if (match.status === "completed" && match.winner) {
        const winnerStanding = standings.find((s) => s.id === match.winner!.id)
        const loser = match.player1?.id === match.winner.id ? match.player2 : match.player1
        const loserStanding = standings.find((s) => s.id === loser?.id)

        if (winnerStanding) {
          winnerStanding.wins++
          winnerStanding.points += 3 // 3 points for a win
        }
        if (loserStanding) {
          loserStanding.losses++
        }
      }
    })

    return standings.sort((a, b) => b.points - a.points || b.wins - b.wins)
  }

  isComplete(): boolean {
    return this.tournament.matches.every((match) => match.status === "completed")
  }
}

"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useSession, signOut } from "next-auth/react";
import React, { useState } from "react";

export default function LogoutButton() {
  const { data: session } = useSession();
  const [showModal, setShowModal] = useState(false);
  if (!session) return null;

  function handleLogoutClick() {
    setShowModal(true);
  }

  function handleCancel() {
    setShowModal(false);
  }

  function handleConfirm() {
    // Simpan semua data localStorage (tetap di localStorage, agar tidak hilang)
    // (Sebenarnya localStorage tidak hilang saat logout, tapi ini untuk memastikan tidak ada proses lain yang menghapus)
    // Bisa juga tambahkan logic sync ke server jika perlu
    setShowModal(false);
    signOut({ callbackUrl: "/login" });
  }

  return (
    <>
      <Button
        className="w-full mt-auto bg-[#ff7f32] hover:bg-[#e86a1f] text-white"
        onClick={handleLogoutClick}
      >
        Logout
      </Button>
      {showModal && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-60">
          <div className="bg-[#222] p-6 rounded-lg shadow-lg max-w-sm w-full text-white z-[9999]">
            <div className="font-bold mb-4">Do you want to save all unsaved data before logging out?</div>
            <div className="flex justify-end gap-2 mt-4">
              <button
                className="px-4 py-2 rounded bg-gray-600 text-white"
                onClick={handleCancel}
              >
                Cancel Logout
              </button>
              <button
                className="px-4 py-2 rounded bg-[#ff7f32] text-white font-semibold"
                onClick={handleConfirm}
              >
                Yes
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
} 
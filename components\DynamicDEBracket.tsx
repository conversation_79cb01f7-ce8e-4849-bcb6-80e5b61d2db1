'use client';

/**
 * DynamicDEBracket.tsx
 *
 * Double Elimination (DE) Tournament Bracket Component
 * - Fixed 16 participants
 * - 4 rounds (Round of 16 → Quarter Finals → Semi Finals → Final)
 * - 15 total matches
 * - Score input and winner determination
 * - Support for random/seeded arrangement
 */

import React, { useState } from 'react';

interface DynamicDEBracketProps {
  participants: string[];
  onMatchUpdate?: (matchId: string, score1: number, score2: number) => void;
  seedingMethod?: 'random' | 'seeded';
  customConfig?: any;
  className?: string;
}

interface MatchScore {
  [matchId: string]: {
    score1: number;
    score2: number;
    winner?: string;
  };
}

export const DynamicDEBracket: React.FC<DynamicDEBracketProps> = ({
  participants,
  onMatchUpdate,
  seedingMethod = 'random',
  customConfig,
  className = ''
}) => {
  const [matchScores, setMatchScores] = useState<MatchScore>({});
  const [showErrorPopup, setShowErrorPopup] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Parse participants from format "0001 ] Player Name"
  const parseParticipant = (participantStr: string) => {
    if (!participantStr || participantStr === 'TBD') {
      return { name: 'TBD', regNumber: '000' };
    }

    const match = participantStr.match(/^(\d+)\s*\]\s*(.+)$/);
    if (match) {
      return {
        name: match[2].trim(),
        regNumber: match[1]
      };
    }

    return { name: participantStr, regNumber: '000' };
  };

  // Ensure we have 16 participants
  const parsedParticipants = [...participants];
  while (parsedParticipants.length < 16) {
    parsedParticipants.push('000 ] TBD');
  }

  const p = parsedParticipants.map(parseParticipant);



  // Handle score updates with validation
  const handleScoreUpdate = (matchId: string, participantIndex: number, score: number) => {
    setMatchScores(prev => {
      const currentMatch = prev[matchId] || { score1: 0, score2: 0 };
      const otherParticipantIndex = participantIndex === 0 ? 1 : 0;
      const otherScore = otherParticipantIndex === 0 ? currentMatch.score1 : currentMatch.score2;

      // Validation: If one participant has score >= 7, the other must have score < 7
      if (score >= 7 && (otherScore ?? 0) >= 7) {
        setErrorMessage('Error: Hanya satu participant yang boleh mendapat score >= 7 poin!');
        setShowErrorPopup(true);
        return prev; // Don't update if validation fails
      }

      const updated = {
        ...prev,
        [matchId]: {
          ...currentMatch,
          [`score${participantIndex + 1}`]: score
        }
      };

      // Determine winner
      const matchScore = updated[matchId];
      if (matchScore.score1 !== undefined && matchScore.score2 !== undefined) {
        if (matchScore.score1 > matchScore.score2) {
          matchScore.winner = `participant1`;
        } else if (matchScore.score2 > matchScore.score1) {
          matchScore.winner = `participant2`;
        }
      }

      // Notify parent component
      if (onMatchUpdate && matchScore.score1 !== undefined && matchScore.score2 !== undefined) {
        onMatchUpdate(matchId, matchScore.score1, matchScore.score2);
      }

      return updated;
    });
  };

  return (
    <div className={`dynamic-de-bracket ${className}`}>
      <style jsx>{`
        .tournament-bracket-container::-webkit-scrollbar {
          width: 5px !important;
          height: 5px !important;
        }
        .tournament-bracket-container::-webkit-scrollbar-track {
          background: #2a2a2a !important;
          border-radius: 3px !important;
          border: 1px solid #404040 !important;
          box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.3) !important;
        }
        .tournament-bracket-container::-webkit-scrollbar-thumb {
          background: #ff7f32 !important;
          border-radius: 3px !important;
          border: 1px solid #2a2a2a !important;
          box-shadow: 0 1px 2px rgba(255, 127, 50, 0.3) !important;
        }
        .tournament-bracket-container::-webkit-scrollbar-thumb:hover {
          background: #ff9f52 !important;
          box-shadow: 0 0 4px rgba(255, 127, 50, 0.5) !important;
          transform: scale(1.1) !important;
        }
        .tournament-bracket-container::-webkit-scrollbar-thumb:active {
          background: #e56b1f !important;
          box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.3) !important;
        }
        .tournament-bracket-container::-webkit-scrollbar-corner {
          background: #2a2a2a !important;
          border: 1px solid #404040 !important;
        }
        /* Horizontal scrollbar specific styling */
        .tournament-bracket-container::-webkit-scrollbar:horizontal {
          height: 5px !important;
        }
        /* Vertical scrollbar specific styling */
        .tournament-bracket-container::-webkit-scrollbar:vertical {
          width: 5px !important;
        }
        /* Firefox scrollbar styling */
        .tournament-bracket-container {
          scrollbar-width: thin !important;
          scrollbar-color: #ff7f32 #2a2a2a !important;
          scroll-behavior: smooth !important;
        }
        /* Custom scrollbar animation */
        .tournament-bracket-container::-webkit-scrollbar-thumb {
          transition: all 0.3s ease !important;
        }
      `}</style>
      {/* Bracket Info */}
      <div className="mb-6 p-4 bg-[#2a2a2a] rounded">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-white">
          <div>
            <span className="text-gray-400">Participants:</span>
            <span className="ml-2 font-semibold">{participants.length}</span>
          </div>
          <div>
            <span className="text-gray-400">Bracket Size:</span>
            <span className="ml-2 font-semibold">16</span>
          </div>
          <div>
            <span className="text-gray-400">Total Rounds:</span>
            <span className="ml-2 font-semibold">4</span>
          </div>
          <div>
            <span className="text-gray-400">Seeding:</span>
            <span className="ml-2 font-semibold capitalize">{seedingMethod}</span>
          </div>
        </div>
      </div>

      {/* Bracket Layout with Native Horizontal & Vertical Scroll */}
      <div
        className="tournament-bracket-container overflow-auto p-6 bg-gradient-to-r from-[#1a1a1a] to-[#2a2a2a] rounded-lg border border-gray-700 shadow-2xl"
        style={{
          maxHeight: '1500px',
          maxWidth: '100%',
          minHeight: '600px'
        }}
      >
        {/* Round Headers */}
        <div className="grid grid-cols-4 gap-12 mb-6" style={{ minWidth: '1800px' }}>
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              Eliminate Round
            </h5>
            <div className="text-xs text-gray-400 mt-1">8 matches</div>
          </div>
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              WB Round 01
            </h5>
            <div className="text-xs text-gray-400 mt-1">4 matches</div>
          </div>
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              WB Round 02
            </h5>
            <div className="text-xs text-gray-400 mt-1">2 matches</div>
          </div>
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              WB ROUND 03 (Semi Final)
            </h5>
            <div className="text-xs text-gray-400 mt-1">1 match</div>
          </div>
        </div>
        <div className="grid grid-cols-4 gap-12 relative" style={{ minHeight: '1200px', minWidth: '1800px' }}>
        {/* WB ROUND 01 - Round of 16 */}
        <div className="relative">
          {/* Match 1 */}
          <BracketMatchComponent
            matchId="R16_1"
            matchNumber={1}
            participant1={p[0]}
            participant2={p[1]}
            matchScore={matchScores['R16_1']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('R16_1', participantIndex, score)
            }
            style={{ position: 'absolute', top: '0px', width: '100%' }}
          />

          {/* Match 2 */}
          <BracketMatchComponent
            matchId="R16_2"
            matchNumber={2}
            participant1={p[2]}
            participant2={p[3]}
            matchScore={matchScores['R16_2']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('R16_2', participantIndex, score)
            }
            style={{ position: 'absolute', top: '80px', width: '100%' }}
          />

          {/* Match 3 */}
          <BracketMatchComponent
            matchId="R16_3"
            matchNumber={3}
            participant1={p[4]}
            participant2={p[5]}
            matchScore={matchScores['R16_3']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('R16_3', participantIndex, score)
            }
            style={{ position: 'absolute', top: '160px', width: '100%' }}
          />

          {/* Match 4 */}
          <BracketMatchComponent
            matchId="R16_4"
            matchNumber={4}
            participant1={p[6]}
            participant2={p[7]}
            matchScore={matchScores['R16_4']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('R16_4', participantIndex, score)
            }
            style={{ position: 'absolute', top: '240px', width: '100%' }}
          />

          {/* Match 5 */}
          <BracketMatchComponent
            matchId="R16_5"
            matchNumber={5}
            participant1={p[8]}
            participant2={p[9]}
            matchScore={matchScores['R16_5']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('R16_5', participantIndex, score)
            }
            style={{ position: 'absolute', top: '320px', width: '100%' }}
          />

          {/* Match 6 */}
          <BracketMatchComponent
            matchId="R16_6"
            matchNumber={6}
            participant1={p[10]}
            participant2={p[11]}
            matchScore={matchScores['R16_6']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('R16_6', participantIndex, score)
            }
            style={{ position: 'absolute', top: '400px', width: '100%' }}
          />

          {/* Match 7 */}
          <BracketMatchComponent
            matchId="R16_7"
            matchNumber={7}
            participant1={p[12]}
            participant2={p[13]}
            matchScore={matchScores['R16_7']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('R16_7', participantIndex, score)
            }
            style={{ position: 'absolute', top: '480px', width: '100%' }}
          />

          {/* Match 8 */}
          <BracketMatchComponent
            matchId="R16_8"
            matchNumber={8}
            participant1={p[14]}
            participant2={p[15]}
            matchScore={matchScores['R16_8']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('R16_8', participantIndex, score)
            }
            style={{ position: 'absolute', top: '560px', width: '100%' }}
          />
        </div>

        {/* WB ROUND 02 - Quarter Finals */}
        <div className="relative">
          {/* Match 9 */}
          <BracketMatchComponent
            matchId="QF1"
            matchNumber={9}
            participant1={{ name: 'Winner R16_1', regNumber: '000' }}
            participant2={{ name: 'Winner R16_2', regNumber: '000' }}
            matchScore={matchScores['QF1']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('QF1', participantIndex, score)
            }
            style={{ position: 'absolute', top: '40px', width: '100%' }}
          />

          {/* Match 10 */}
          <BracketMatchComponent
            matchId="QF2"
            matchNumber={10}
            participant1={{ name: 'Winner R16_3', regNumber: '000' }}
            participant2={{ name: 'Winner R16_4', regNumber: '000' }}
            matchScore={matchScores['QF2']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('QF2', participantIndex, score)
            }
            style={{ position: 'absolute', top: '200px', width: '100%' }}
          />

          {/* Match 11 */}
          <BracketMatchComponent
            matchId="QF3"
            matchNumber={11}
            participant1={{ name: 'Winner R16_5', regNumber: '000' }}
            participant2={{ name: 'Winner R16_6', regNumber: '000' }}
            matchScore={matchScores['QF3']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('QF3', participantIndex, score)
            }
            style={{ position: 'absolute', top: '360px', width: '100%' }}
          />

          {/* Match 12 */}
          <BracketMatchComponent
            matchId="QF4"
            matchNumber={12}
            participant1={{ name: 'Winner R16_7', regNumber: '000' }}
            participant2={{ name: 'Winner R16_8', regNumber: '000' }}
            matchScore={matchScores['QF4']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('QF4', participantIndex, score)
            }
            style={{ position: 'absolute', top: '520px', width: '100%' }}
          />
        </div>

        {/* WB ROUND 03 - Semi Finals */}
        <div className="relative">
          {/* Match 13 */}
          <BracketMatchComponent
            matchId="SF1"
            matchNumber={13}
            participant1={{ name: 'Winner QF1', regNumber: '000' }}
            participant2={{ name: 'Winner QF2', regNumber: '000' }}
            matchScore={matchScores['SF1']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('SF1', participantIndex, score)
            }
            style={{ position: 'absolute', top: '120px', width: '100%' }}
          />

          {/* Match 14 */}
          <BracketMatchComponent
            matchId="SF2"
            matchNumber={14}
            participant1={{ name: 'Winner QF3', regNumber: '000' }}
            participant2={{ name: 'Winner QF4', regNumber: '000' }}
            matchScore={matchScores['SF2']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('SF2', participantIndex, score)
            }
            style={{ position: 'absolute', top: '440px', width: '100%' }}
          />
        </div>

        {/* WB ROUND 04 - Final */}
        <div className="relative">
          {/* Match 15 */}
          <BracketMatchComponent
            matchId="F1"
            matchNumber={15}
            participant1={{ name: 'Winner SF1', regNumber: '000' }}
            participant2={{ name: 'Winner SF2', regNumber: '000' }}
            matchScore={matchScores['F1']}
            onScoreUpdate={(participantIndex, score) =>
              handleScoreUpdate('F1', participantIndex, score)
            }
            style={{ position: 'absolute', top: '305px', width: '100%' }}
          />
        </div>
      </div>

      {/* Loser's Bracket Section */}
      <div style={{ marginTop: '-500px' }}>


        {/* Loser's Bracket Round Headers */}
        <div className="grid grid-cols-6 gap-12" style={{ minWidth: '2700px', marginTop: '0px' }}>
          <div></div> {/* Empty space for alignment with WB ROUND 01 */}
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              LB ROUND 01
            </h5>
            <div className="text-xs text-gray-400 mt-1">4 matches</div>
          </div>
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              LB ROUND 02 (Semi Final)
            </h5>
            <div className="text-xs text-gray-400 mt-1">4 matches</div>
          </div>
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              LB ROUND 03 (Final)
            </h5>
            <div className="text-xs text-gray-400 mt-1">2 matches</div>
          </div>
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              LB ROUND 04
            </h5>
            <div className="text-xs text-gray-400 mt-1">2 matches</div>
          </div>
          <div className="text-center">
            <h5 className="font-semibold text-white text-sm py-2 bg-[#404040] rounded">
              LB ROUND 05 (Grand Final)
            </h5>
            <div className="text-xs text-gray-400 mt-1">1 match</div>
          </div>
        </div>

        {/* Loser's Bracket Layout - Placeholder */}
        <div className="grid grid-cols-6 gap-12 relative" style={{ minHeight: '800px', minWidth: '2700px' }}>
          <div></div> {/* Empty space for alignment */}
          <div className="relative">
            {/* LB Match 1 */}
            <BracketMatchComponent
              matchId="LB1"
              matchNumber={16}
              participant1={{ name: 'Loser R16_1', regNumber: '000' }}
              participant2={{ name: 'Loser R16_2', regNumber: '000' }}
              matchScore={matchScores['LB1']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB1', participantIndex, score)
              }
              style={{ position: 'absolute', top: '65px', width: '100%' }}
            />

            {/* LB Match 2 */}
            <BracketMatchComponent
              matchId="LB2"
              matchNumber={17}
              participant1={{ name: 'Loser R16_3', regNumber: '000' }}
              participant2={{ name: 'Loser R16_4', regNumber: '000' }}
              matchScore={matchScores['LB2']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB2', participantIndex, score)
              }
              style={{ position: 'absolute', top: '145px', width: '100%' }}
            />

            {/* LB Match 3 */}
            <BracketMatchComponent
              matchId="LB3"
              matchNumber={18}
              participant1={{ name: 'Loser R16_5', regNumber: '000' }}
              participant2={{ name: 'Loser R16_6', regNumber: '000' }}
              matchScore={matchScores['LB3']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB3', participantIndex, score)
              }
              style={{ position: 'absolute', top: '225px', width: '100%' }}
            />

            {/* LB Match 4 */}
            <BracketMatchComponent
              matchId="LB4"
              matchNumber={19}
              participant1={{ name: 'Loser R16_7', regNumber: '000' }}
              participant2={{ name: 'Loser R16_8', regNumber: '000' }}
              matchScore={matchScores['LB4']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB4', participantIndex, score)
              }
              style={{ position: 'absolute', top: '305px', width: '100%' }}
            />
          </div>
          <div className="relative">
            {/* LB Match 5 */}
            <BracketMatchComponent
              matchId="LB5"
              matchNumber={20}
              participant1={{ name: 'Winner LB1', regNumber: '000' }}
              participant2={{ name: 'Loser WB R01_1', regNumber: '000' }}
              matchScore={matchScores['LB5']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB5', participantIndex, score)
              }
              style={{ position: 'absolute', top: '25px', width: '100%' }}
            />

            {/* LB Match 6 */}
            <BracketMatchComponent
              matchId="LB6"
              matchNumber={21}
              participant1={{ name: 'Winner LB2', regNumber: '000' }}
              participant2={{ name: 'Loser WB R01_2', regNumber: '000' }}
              matchScore={matchScores['LB6']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB6', participantIndex, score)
              }
              style={{ position: 'absolute', top: '105px', width: '100%' }}
            />

            {/* LB Match 7 */}
            <BracketMatchComponent
              matchId="LB7"
              matchNumber={22}
              participant1={{ name: 'Winner LB3', regNumber: '000' }}
              participant2={{ name: 'Loser WB R01_3', regNumber: '000' }}
              matchScore={matchScores['LB7']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB7', participantIndex, score)
              }
              style={{ position: 'absolute', top: '185px', width: '100%' }}
            />

            {/* LB Match 8 */}
            <BracketMatchComponent
              matchId="LB8"
              matchNumber={23}
              participant1={{ name: 'Winner LB4', regNumber: '000' }}
              participant2={{ name: 'Loser WB R01_4', regNumber: '000' }}
              matchScore={matchScores['LB8']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB8', participantIndex, score)
              }
              style={{ position: 'absolute', top: '265px', width: '100%' }}
            />
          </div>
          <div className="relative">
            {/* LB Match 9 */}
            <BracketMatchComponent
              matchId="LB9"
              matchNumber={24}
              participant1={{ name: 'Winner LB5', regNumber: '000' }}
              participant2={{ name: 'Winner LB6', regNumber: '000' }}
              matchScore={matchScores['LB9']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB9', participantIndex, score)
              }
              style={{ position: 'absolute', top: '65px', width: '100%' }}
            />

            {/* LB Match 10 */}
            <BracketMatchComponent
              matchId="LB10"
              matchNumber={25}
              participant1={{ name: 'Winner LB7', regNumber: '000' }}
              participant2={{ name: 'Winner LB8', regNumber: '000' }}
              matchScore={matchScores['LB10']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB10', participantIndex, score)
              }
              style={{ position: 'absolute', top: '225px', width: '100%' }}
            />
          </div>
          <div className="relative">
            {/* LB Match 11 - LB Round 4 */}
            <BracketMatchComponent
              matchId="LB11"
              matchNumber={26}
              participant1={{ name: 'Winner LB9', regNumber: '000' }}
              participant2={{ name: 'Loser WB Semi 1', regNumber: '000' }}
              matchScore={matchScores['LB11']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB11', participantIndex, score)
              }
              style={{ position: 'absolute', top: '85px', width: '100%' }}
            />

            {/* LB Match 12 - LB Round 4 */}
            <BracketMatchComponent
              matchId="LB12"
              matchNumber={27}
              participant1={{ name: 'Winner LB10', regNumber: '000' }}
              participant2={{ name: 'Loser WB Semi 2', regNumber: '000' }}
              matchScore={matchScores['LB12']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB12', participantIndex, score)
              }
              style={{ position: 'absolute', top: '205px', width: '100%' }}
            />
          </div>
          <div className="relative">
            {/* LB Match 13 - LB Round 5 (Grand Final) */}
            <BracketMatchComponent
              matchId="LB13"
              matchNumber={28}
              participant1={{ name: 'Winner LB11', regNumber: '000' }}
              participant2={{ name: 'Winner LB12', regNumber: '000' }}
              matchScore={matchScores['LB13']}
              onScoreUpdate={(participantIndex, score) =>
                handleScoreUpdate('LB13', participantIndex, score)
              }
              style={{ position: 'absolute', top: '145px', width: '100%' }}
            />
          </div>
        </div>
      </div>
      </div>



      {/* Error Popup */}
      {showErrorPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50">
          <div className="bg-[#2a2a2a] rounded-lg p-6 max-w-md mx-4 shadow-2xl border border-gray-600">
            <div className="mb-4">
              <h3 className="text-lg font-medium text-white mb-3">Peringatan Validasi Score</h3>
              <p className="text-gray-300 text-sm leading-relaxed">{errorMessage}</p>
            </div>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowErrorPopup(false)}
                className="px-4 py-2 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => setShowErrorPopup(false)}
                className="px-4 py-2 bg-orange-500 text-white rounded text-sm hover:bg-orange-600 transition-colors"
              >
                OK
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Individual Match Component
interface BracketMatchComponentProps {
  matchId: string;
  matchNumber: number;
  participant1: { name: string; regNumber: string };
  participant2: { name: string; regNumber: string };
  matchScore?: {
    score1: number;
    score2: number;
    winner?: string;
  };
  onScoreUpdate: (participantIndex: number, score: number) => void;
  style?: React.CSSProperties;
}

const BracketMatchComponent: React.FC<BracketMatchComponentProps> = ({
  matchId,
  matchNumber,
  participant1,
  participant2,
  matchScore,
  onScoreUpdate,
  style
}) => {
  return (
    <div style={style} className="relative">
      <div className="bg-[#404040] rounded border border-gray-600 relative z-10">
        {/* Participants */}
        <div className="relative">
          {/* Participant 1 */}
          <div className="flex items-center justify-between px-3 py-1">
            <div className={`flex-1 min-w-0 ml-5 rounded px-2 py-1 ${
              (matchScore?.score1 ?? 0) >= 7 ? 'bg-orange-500' : ''
            }`}>
              <p className="text-xs font-medium text-white">
                {participant1.regNumber} ] {participant1.name}
              </p>
            </div>
            <input
              type="number"
              className="w-12 h-6 ml-2 rounded bg-[#333] text-center text-xs border border-gray-600 text-white font-bold"
              min={0}
              value={matchScore?.score1 ?? 0}
              onChange={(e) => onScoreUpdate(0, parseInt(e.target.value) || 0)}
              disabled={participant1.name === 'TBD'}
            />
          </div>

          {/* Center Border Line */}
          <div className="absolute left-8 right-16 top-1/2 transform -translate-y-1/2 border-b border-gray-600"></div>

          {/* Match Number - positioned between participants */}
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-xs font-bold z-20">
            {matchNumber}
          </div>

          {/* Participant 2 */}
          <div className="flex items-center justify-between px-3 py-1">
            <div className={`flex-1 min-w-0 ml-5 rounded px-2 py-1 ${
              (matchScore?.score2 ?? 0) >= 7 ? 'bg-orange-500' : ''
            }`}>
              <p className="text-xs font-medium text-white">
                {participant2.regNumber} ] {participant2.name}
              </p>
            </div>
            <input
              type="number"
              className="w-12 h-6 ml-2 rounded bg-[#333] text-center text-xs border border-gray-600 text-white font-bold"
              min={0}
              value={matchScore?.score2 ?? 0}
              onChange={(e) => onScoreUpdate(1, parseInt(e.target.value) || 0)}
              disabled={participant2.name === 'TBD'}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DynamicDEBracket;

import NextAuth from "next-auth";

declare module "next-auth" {
  interface Session {
    user?: {
      id?: string;
      name?: string | null;
      email?: string | null;
      image?: string | null;
      fullName?: string | null;
      phone?: string | null;
      bio?: string | null;
    };
  }
  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
    fullName?: string | null;
    phone?: string | null;
    bio?: string | null;
  }
} 
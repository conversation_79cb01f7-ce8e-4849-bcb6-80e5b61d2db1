"use client";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import DynamicPageHeader, { QuickActionButton, PageStatus } from "@/components/DynamicPageHeader";
import { Plus, Trophy, User, Settings } from "lucide-react";
import TournamentListSection from "@/components/TournamentListSection";

export default function DashboardPage() {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "authenticated" && !session) {
      router.refresh();
    }
  }, [status, session, router]);

  if (status === "loading") {
    return <div className="min-h-screen bg-[#333333] text-white flex flex-col items-center justify-center">Loading...</div>;
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-[#333333] text-white flex flex-col items-center justify-center">
        <h1 className="text-3xl font-bold mb-4">Dashboard</h1>
        <p>You are not logged in. Please <Link href="/login" className="underline">login</Link> first.</p>
      </div>
    );
  }

  // Contoh: tampilkan data user
  return (
    <div className="min-h-screen bg-[#333333] text-white">
      {/* Dynamic Page Header */}
      <DynamicPageHeader
        title="Dashboard"
        subtitle={`Welcome back, ${session.user?.name || "User"}! Here's your tournament overview.`}
        showStats={true}
      >
        <QuickActionButton
          href="/create-tournament"
          icon={Plus}
          label="Create Tournament"
          variant="primary"
        />
      </DynamicPageHeader>

      <div className="container mx-auto px-4 py-8">
        {/* Tournament List Section */}
        <div className="mb-8">
          <TournamentListSection />
        </div>
      </div>
    </div>
  );
} 
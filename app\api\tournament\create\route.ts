import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { prisma } from "@/lib/prisma";

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user?.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const data = await req.json();
  try {
    const tournament = await prisma.tournament.create({
      data: {
        name: data.name,
        description: data.description || "",
        game: data.game || "Domino",
        startDate: new Date(data.startDate),
        endDate: new Date(data.endDate),
        status: data.status || "upcoming",
        type: data.type || data.format || "single elimination",
        maxTeams: data.maxTeams || data.participants || 0,

        // Tournament Configuration
        typeOfStage: data.typeOfStage,
        groupStageFormat: data.groupStageFormat,
        groupAdvance: data.groupAdvance,
        finalStageFormat: data.finalStageFormat,
        matchFormat: data.matchFormat,

        organizerId: session.user.id,
      },
    });
    return NextResponse.json({ success: true, tournament });
  } catch (err) {
    return NextResponse.json({ error: "Failed to create tournament" }, { status: 500 });
  }
} 
"use client";
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { useSearchParams, useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { formatDistanceToNow, format } from "date-fns"
import { DynamicBreadcrumb } from "@/components/DynamicSidebar"

export default function TournamentsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Redirect to dashboard with tab parameter
  useEffect(() => {
    const tab = searchParams.get("tab") || "upcoming"
    router.replace(`/user-dashboard?tab=${tab}`)
  }, [router, searchParams])

  // Loading state while redirecting
  return (
    <div className="min-h-screen bg-[#333333] text-white flex flex-col items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Redirecting...</h1>
        <p className="text-gray-400">Taking you to the dashboard...</p>
      </div>
    </div>
  )
}


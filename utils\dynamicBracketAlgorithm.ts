/**
 * Dynamic Tournament Bracket Algorithm
 * Supports automatic positioning for any number of participants
 * Created by: Panglima's Dynamic Bracket System
 */

export interface BracketMatch {
  id: string;
  round: number;
  position: number;
  participants: string[];
  winner?: string;
  nextMatchId?: string;
  verticalPosition: number;
  horizontalPosition: number;
}

export interface BracketRound {
  roundNumber: number;
  roundName: string;
  matches: BracketMatch[];
  totalMatches: number;
}

export interface BracketConfiguration {
  participants: number;
  containerWidth: number;
  containerHeight: number;
  matchHeight: number;
  roundSpacing: number;
  verticalSpacing: number;
  centerAlignment: boolean;
}

export class DynamicBracketGenerator {
  private config: BracketConfiguration;
  private totalRounds: number;
  private bracketStructure: BracketRound[] = [];

  constructor(config: BracketConfiguration) {
    this.config = config;
    this.totalRounds = Math.ceil(Math.log2(config.participants));
    this.generateBracketStructure();
  }

  /**
   * Generate complete bracket structure
   */
  private generateBracketStructure(): void {
    this.bracketStructure = [];
    
    for (let round = 1; round <= this.totalRounds; round++) {
      const matchesInRound = Math.pow(2, this.totalRounds - round);
      const roundName = this.getRoundName(round, this.totalRounds);
      
      const matches: BracketMatch[] = [];
      
      for (let matchIndex = 0; matchIndex < matchesInRound; matchIndex++) {
        const match: BracketMatch = {
          id: `R${round}M${matchIndex + 1}`,
          round,
          position: matchIndex,
          participants: round === 1 ? this.getInitialParticipants(matchIndex) : [`Winner R${round-1}M${matchIndex*2+1}`, `Winner R${round-1}M${matchIndex*2+2}`],
          verticalPosition: this.calculateVerticalPosition(round, matchIndex, matchesInRound),
          horizontalPosition: this.calculateHorizontalPosition(round),
          nextMatchId: round < this.totalRounds ? `R${round + 1}M${Math.floor(matchIndex / 2) + 1}` : undefined
        };
        
        matches.push(match);
      }
      
      this.bracketStructure.push({
        roundNumber: round,
        roundName,
        matches,
        totalMatches: matchesInRound
      });
    }
  }

  /**
   * Calculate vertical position for centered alignment
   */
  private calculateVerticalPosition(round: number, matchIndex: number, totalMatches: number): number {
    if (!this.config.centerAlignment) {
      // Simple equal spacing
      const spacing = this.config.containerHeight / (totalMatches + 1);
      return spacing * (matchIndex + 1);
    }

    // Center alignment based on previous round
    if (round === 1) {
      // First round: equal spacing
      const spacing = this.config.containerHeight / (totalMatches + 1);
      return spacing * (matchIndex + 1);
    } else {
      // Subsequent rounds: center between previous matches
      const prevRound = this.bracketStructure[round - 2];
      const match1Pos = prevRound.matches[matchIndex * 2]?.verticalPosition || 0;
      const match2Pos = prevRound.matches[matchIndex * 2 + 1]?.verticalPosition || 0;
      return (match1Pos + match2Pos) / 2;
    }
  }

  /**
   * Calculate horizontal position
   */
  private calculateHorizontalPosition(round: number): number {
    const roundSpacing = this.config.containerWidth / this.totalRounds;
    return roundSpacing * (round - 1);
  }

  /**
   * Get round name based on position
   */
  private getRoundName(round: number, totalRounds: number): string {
    const roundsFromEnd = totalRounds - round;
    
    switch (roundsFromEnd) {
      case 0: return 'Finals';
      case 1: return 'Semi Finals';
      case 2: return 'Quarter Finals';
      case 3: return 'Round of 16';
      case 4: return 'Round of 32';
      case 5: return 'Round of 64';
      default: return `Round ${round}`;
    }
  }

  /**
   * Get initial participants for first round
   */
  private getInitialParticipants(matchIndex: number): string[] {
    const participant1 = `Participant ${matchIndex * 2 + 1}`;
    const participant2 = `Participant ${matchIndex * 2 + 2}`;
    return [participant1, participant2];
  }

  /**
   * Get complete bracket structure
   */
  public getBracketStructure(): BracketRound[] {
    return this.bracketStructure;
  }

  /**
   * Get grid configuration for CSS
   */
  public getGridConfiguration() {
    return {
      columns: this.totalRounds,
      roundSpacing: this.config.roundSpacing,
      verticalSpacing: this.config.verticalSpacing,
      containerWidth: this.config.containerWidth,
      containerHeight: this.config.containerHeight
    };
  }

  /**
   * Calculate optimal spacing based on participants
   */
  public static calculateOptimalSpacing(participants: number, containerHeight: number): number {
    const firstRoundMatches = participants / 2;
    const minSpacing = 80; // Minimum space between matches
    const calculatedSpacing = (containerHeight - (firstRoundMatches * 60)) / (firstRoundMatches + 1);
    return Math.max(minSpacing, calculatedSpacing);
  }

  /**
   * Validate participant count (must be power of 2)
   */
  public static validateParticipantCount(participants: number): boolean {
    return participants > 0 && (participants & (participants - 1)) === 0;
  }

  /**
   * Get next valid participant count
   */
  public static getNextValidParticipantCount(participants: number): number {
    if (participants <= 0) return 2;
    return Math.pow(2, Math.ceil(Math.log2(participants)));
  }

  /**
   * Generate seeding arrangement
   */
  public generateSeededBracket(seeds: string[]): BracketRound[] {
    // Implement seeding logic here
    const seededStructure = [...this.bracketStructure];
    
    if (seeds.length >= this.config.participants) {
      // Apply seeding to first round
      const firstRound = seededStructure[0];
      const seededPairs = this.createSeededPairs(seeds);
      
      firstRound.matches.forEach((match, index) => {
        if (seededPairs[index]) {
          match.participants = seededPairs[index];
        }
      });
    }
    
    return seededStructure;
  }

  /**
   * Create seeded pairs following tournament seeding rules
   */
  private createSeededPairs(seeds: string[]): string[][] {
    const pairs: string[][] = [];
    const n = this.config.participants;
    
    // Standard tournament seeding
    for (let i = 0; i < n / 2; i++) {
      const seed1 = i;
      const seed2 = n - 1 - i;
      pairs.push([seeds[seed1], seeds[seed2]]);
    }
    
    return pairs;
  }
}

/**
 * Utility functions for bracket generation
 */
export const BracketUtils = {
  /**
   * Generate default configuration
   */
  getDefaultConfig: (participants: number): BracketConfiguration => ({
    participants,
    containerWidth: 1200,
    containerHeight: 800,
    matchHeight: 60,
    roundSpacing: 200,
    verticalSpacing: DynamicBracketGenerator.calculateOptimalSpacing(participants, 800),
    centerAlignment: true
  }),

  /**
   * Calculate responsive dimensions
   */
  getResponsiveDimensions: (participants: number, screenWidth: number) => {
    const rounds = Math.ceil(Math.log2(participants));
    const minRoundWidth = 180;
    const totalMinWidth = rounds * minRoundWidth;
    
    return {
      containerWidth: Math.max(totalMinWidth, screenWidth * 0.9),
      roundSpacing: Math.max(minRoundWidth, (screenWidth * 0.9) / rounds),
      containerHeight: Math.max(600, participants * 40)
    };
  }
};

import { PrismaAdapter } from "@auth/prisma-adapter"
import { NextAuthOptions } from "next-auth"
import { prisma } from "@/lib/prisma"
import Cred<PERSON>sProvider from "next-auth/providers/credentials"
import bcrypt from "bcryptjs"
import <PERSON><PERSON><PERSON> from "next-auth/next"
import { JWT } from "next-auth/jwt"

// ... (type declarations tetap sama)

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          if (process.env.NODE_ENV === "development") {
            console.error("No credentials provided")
          }
          throw new Error("Invalid credentials")
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user) {
          if (process.env.NODE_ENV === "development") {
            console.error("User not found for email:", credentials.email)
          }
          throw new Error("No user found with this email")
        }

        if (!user.password) {
          if (process.env.NODE_ENV === "development") {
            console.error("User has no password set:", credentials.email)
          }
          throw new Error("User has no password set")
        }

        const isCorrectPassword = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isCorrectPassword) {
          if (process.env.NODE_ENV === "development") {
            console.error("Password does not match for user:", credentials.email)
          }
          throw new Error("Incorrect password")
        }

        return user
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.name = user.name;
        token.email = user.email;
        token.fullName = user.fullName;
        token.phone = user.phone;
        token.bio = user.bio;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        (session.user as { id?: string; name?: string | null; email?: string | null; image?: string | null; fullName?: string | null; phone?: string | null; bio?: string | null }).id = token.id as string;
        session.user.name = token.name as string;
        session.user.email = token.email as string;
        (session.user as any).fullName = token.fullName as string;
        (session.user as any).phone = token.phone as string;
        (session.user as any).bio = token.bio as string;
      }
      return session;
    },
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 hari
    updateAge: 24 * 60 * 60, // Update session setiap 24 jam
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 hari
  },
  pages: {
    signIn: '/login',
    error: '/error',
  },
  // ... (rest of your config, pages, session, callbacks, etc)
  // (tidak perlu diubah)
}

const handler = NextAuth(authOptions)
export { handler as GET, handler as POST }
'use client';

/**
 * Test Page untuk DynamicDEBracket.tsx
 * <PERSON><PERSON> demo untuk melihat hasil Double Elimination Bracket
 */

import React, { useState } from 'react';
import { DynamicDEBracket } from '@/components/DynamicDEBracket';

export default function TestDEBracketPage() {
  const [seedingMethod, setSeedingMethod] = useState<'random' | 'seeded'>('random');
  
  // Sample participants data dengan format yang benar
  const sampleParticipants = [
    '0001 ] Atlet 001 & Atlet 002',
    '0002 ] Atlet 003 & Atlet 004',
    '0003 ] Atlet 005 & Atlet 006',
    '0004 ] Atlet 007 & Atlet 008',
    '0005 ] Atlet 009 & Atlet 010',
    '0006 ] Atlet 011 & Atlet 012',
    '0007 ] Atlet 013 & Atlet 014',
    '0008 ] Atlet 015 & Atlet 016',
    '0009 ] Atlet 017 & Atlet 018',
    '0010 ] Atlet 019 & Atlet 020',
    '0011 ] Atlet 021 & Atlet 022',
    '0012 ] Atlet 023 & Atlet 024',
    '0013 ] Atlet 025 & Atlet 026',
    '0014 ] Atlet 027 & Atlet 028',
    '0015 ] Atlet 029 & Atlet 030',
    '0016 ] Atlet 031 & Atlet 032'
  ];

  const handleMatchUpdate = (matchId: string, score1: number, score2: number) => {
    console.log(`Match ${matchId} updated:`, { score1, score2 });
  };

  const handleSeedingChange = (method: 'random' | 'seeded') => {
    setSeedingMethod(method);
  };

  return (
    <div className="min-h-screen bg-[#1a1a1a] text-white">
      {/* Header */}
      <div className="bg-[#2a2a2a] border-b border-gray-700 sticky top-0 z-40">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">
                Test Double Elimination Bracket
              </h1>
              <p className="text-gray-400 text-sm mt-1">
                Demo halaman untuk DynamicDEBracket.tsx
              </p>
            </div>
            
            {/* Controls */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm text-gray-400">Seeding Method:</label>
                <select
                  value={seedingMethod}
                  onChange={(e) => handleSeedingChange(e.target.value as 'random' | 'seeded')}
                  className="bg-[#404040] text-white text-sm px-3 py-1 rounded border border-gray-600"
                >
                  <option value="random">Random</option>
                  <option value="seeded">Seeded</option>
                </select>
              </div>
              
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-orange-500 text-white text-sm rounded hover:bg-orange-600 transition-colors"
              >
                Reset Bracket
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        {/* Bracket Info Card */}
        <div className="mb-8 p-6 bg-[#2a2a2a] rounded-lg border border-gray-700">
          <h2 className="text-lg font-semibold text-white mb-4">Informasi Bracket</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">{sampleParticipants.length}</div>
              <div className="text-sm text-gray-400">Participants</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">15</div>
              <div className="text-sm text-gray-400">Total Matches</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500">DE</div>
              <div className="text-sm text-gray-400">Bracket Type</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-500 capitalize">{seedingMethod}</div>
              <div className="text-sm text-gray-400">Seeding Method</div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mb-8 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h3 className="text-blue-400 font-medium mb-2">Cara Penggunaan:</h3>
          <ul className="text-sm text-blue-200 space-y-1">
            <li>• Input score pada kolom score untuk setiap participant</li>
            <li>• Score >= 7 akan mengubah background participant menjadi orange</li>
            <li>• Hanya satu participant per match yang boleh mendapat score >= 7</li>
            <li>• Winner's Bracket: Pemenang lanjut, yang kalah masuk Loser's Bracket</li>
            <li>• Loser's Bracket: Sistem eliminasi kedua dengan progression kompleks</li>
            <li>• Grand Final: Winner WB vs Winner LB (dengan potential reset)</li>
            <li>• <strong>Native Horizontal & Vertical Scroll:</strong> Gunakan scrollbar untuk navigasi bracket</li>
          </ul>
        </div>

        {/* Tournament Rules */}
        <div className="mb-8 p-4 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
          <h3 className="text-yellow-400 font-medium mb-2">Aturan PORDI:</h3>
          <ul className="text-sm text-yellow-200 space-y-1">
            <li>• Tidak ada hasil seri/draw dalam pertandingan</li>
            <li>• Pemenang mendapat 1 poin, yang kalah mendapat 0 poin</li>
            <li>• Score >= 7 menandakan pemenang match</li>
            <li>• Double Elimination: 2 kesempatan untuk setiap participant</li>
            <li>• Grand Final Reset: Jika LB winner mengalahkan WB winner</li>
          </ul>
        </div>

        {/* Navigation Info */}
        <div className="mb-8 p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
          <h3 className="text-green-400 font-medium mb-2">Navigasi Bracket:</h3>
          <ul className="text-sm text-green-200 space-y-1">
            <li>• <strong>Scroll Horizontal:</strong> Navigasi antar round (Eliminate → WB Round 01 → WB Round 02 → Final)</li>
            <li>• <strong>Scroll Vertical:</strong> Navigasi antar match dalam satu round</li>
            <li>• <strong>Scrollbar Orange:</strong> Warna konsisten dengan tema PORDI</li>
            <li>• <strong>Smooth Scroll:</strong> Animasi halus untuk pengalaman yang lebih baik</li>
          </ul>
        </div>

        {/* Dynamic DE Bracket Component */}
        <div className="bg-[#2a2a2a] rounded-lg border border-gray-700 p-6">
          <DynamicDEBracket
            participants={sampleParticipants}
            onMatchUpdate={handleMatchUpdate}
            seedingMethod={seedingMethod}
            className="w-full"
          />
        </div>

        {/* Footer Info */}
        <div className="mt-8 text-center text-gray-400 text-sm">
          <p>PORDI Tournament Management Platform - Double Elimination Bracket Test</p>
          <p className="mt-1">Developed by Panglima's Dynamic Bracket System</p>
        </div>
      </div>
    </div>
  );
}

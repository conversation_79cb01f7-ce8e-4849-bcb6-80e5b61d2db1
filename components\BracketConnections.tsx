'use client';

import React from 'react';

interface BracketConnectionsProps {
  roundIndex: number;
  matchIndex: number;
  totalMatches: number;
  matchHeight: number;
  matchSpacing: number;
  roundSpacing: number;
  isLastRound?: boolean;
}

export const BracketConnections: React.FC<BracketConnectionsProps> = ({
  roundIndex,
  matchIndex,
  totalMatches,
  matchHeight = 80,
  matchSpacing = 16,
  roundSpacing = 200,
  isLastRound = false
}) => {
  // Don't show connections for the last round
  if (isLastRound) return null;

  const isEvenMatch = matchIndex % 2 === 0;
  const pairIndex = Math.floor(matchIndex / 2);
  
  // Calculate positions
  const horizontalLineLength = roundSpacing * 0.4; // 40% of round spacing
  const verticalLineLength = matchHeight + matchSpacing;
  
  return (
    <div className="absolute left-full top-1/2 transform -translate-y-1/2 pointer-events-none">
      <svg 
        width={roundSpacing} 
        height={verticalLineLength * 2} 
        className="absolute top-0 left-0"
        style={{ transform: 'translateY(-50%)' }}
      >
        {/* Horizontal line from current match */}
        <line
          x1={0}
          y1={verticalLineLength}
          x2={horizontalLineLength}
          y2={verticalLineLength}
          stroke="#6b7280"
          strokeWidth="2"
          className="bracket-connection-line"
        />
        
        {/* Vertical connector line */}
        {isEvenMatch ? (
          // For even matches (top of pair), draw line down
          <line
            x1={horizontalLineLength}
            y1={verticalLineLength}
            x2={horizontalLineLength}
            y2={verticalLineLength + (matchHeight + matchSpacing) / 2}
            stroke="#6b7280"
            strokeWidth="2"
            className="bracket-connection-line"
          />
        ) : (
          // For odd matches (bottom of pair), draw line up
          <line
            x1={horizontalLineLength}
            y1={verticalLineLength}
            x2={horizontalLineLength}
            y2={verticalLineLength - (matchHeight + matchSpacing) / 2}
            stroke="#6b7280"
            strokeWidth="2"
            className="bracket-connection-line"
          />
        )}
        
        {/* Horizontal line to next match (only for the connecting match) */}
        {isEvenMatch && (
          <line
            x1={horizontalLineLength}
            y1={verticalLineLength + (matchHeight + matchSpacing) / 2}
            x2={roundSpacing * 0.8}
            y2={verticalLineLength + (matchHeight + matchSpacing) / 2}
            stroke="#6b7280"
            strokeWidth="2"
            className="bracket-connection-line"
          />
        )}
      </svg>
    </div>
  );
};

// Enhanced connection component with better positioning
interface EnhancedBracketConnectionsProps {
  fromMatch: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  toMatch: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  connectionStyle?: 'straight' | 'curved';
}

export const EnhancedBracketConnections: React.FC<EnhancedBracketConnectionsProps> = ({
  fromMatch,
  toMatch,
  connectionStyle = 'straight'
}) => {
  const startX = fromMatch.x + fromMatch.width;
  const startY = fromMatch.y + fromMatch.height / 2;
  const endX = toMatch.x;
  const endY = toMatch.y + toMatch.height / 2;
  
  const midX = startX + (endX - startX) / 2;
  
  return (
    <svg 
      className="absolute top-0 left-0 pointer-events-none z-0"
      style={{
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0
      }}
    >
      {connectionStyle === 'curved' ? (
        <path
          d={`M ${startX} ${startY} Q ${midX} ${startY} ${midX} ${endY} T ${endX} ${endY}`}
          stroke="#6b7280"
          strokeWidth="2"
          fill="none"
          className="bracket-connection-line"
        />
      ) : (
        <g>
          {/* Horizontal line from match */}
          <line
            x1={startX}
            y1={startY}
            x2={midX}
            y2={startY}
            stroke="#6b7280"
            strokeWidth="2"
            className="bracket-connection-line"
          />
          
          {/* Vertical line */}
          <line
            x1={midX}
            y1={startY}
            x2={midX}
            y2={endY}
            stroke="#6b7280"
            strokeWidth="2"
            className="bracket-connection-line"
          />
          
          {/* Horizontal line to next match */}
          <line
            x1={midX}
            y1={endY}
            x2={endX}
            y2={endY}
            stroke="#6b7280"
            strokeWidth="2"
            className="bracket-connection-line"
          />
        </g>
      )}
    </svg>
  );
};

// Simple bracket connector for basic layouts
export const SimpleBracketConnector: React.FC<{
  show: boolean;
  matchPosition: number;
  roundIndex: number;
}> = ({ show, matchPosition, roundIndex }) => {
  if (!show) return null;
  
  const isEvenMatch = matchPosition % 2 === 0;
  const lineColor = "#6b7280";
  
  return (
    <div className="absolute left-full top-1/2 transform -translate-y-1/2 pointer-events-none z-0">
      {/* Horizontal line going right */}
      <div 
        className="absolute top-0 left-0 bg-gray-500"
        style={{
          width: '32px',
          height: '2px',
          transform: 'translateY(-1px)'
        }}
      />
      
      {/* Vertical connector */}
      {isEvenMatch ? (
        // Top match of pair - line goes down
        <div 
          className="absolute left-8 top-0 bg-gray-500"
          style={{
            width: '2px',
            height: '48px',
            transform: 'translateY(-1px)'
          }}
        />
      ) : (
        // Bottom match of pair - line goes up  
        <div 
          className="absolute left-8 bg-gray-500"
          style={{
            width: '2px',
            height: '48px',
            transform: 'translateY(-47px)'
          }}
        />
      )}
      
      {/* Horizontal line to next match (only for top match of pair) */}
      {isEvenMatch && (
        <div 
          className="absolute left-8 bg-gray-500"
          style={{
            width: '32px',
            height: '2px',
            top: '47px',
            transform: 'translateY(-1px)'
          }}
        />
      )}
    </div>
  );
};

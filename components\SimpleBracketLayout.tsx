'use client';

import React, { useState } from 'react';

interface SimpleBracketLayoutProps {
  participants: string[];
  onMatchUpdate?: (matchId: string, score1: number, score2: number) => void;
}

const SimpleBracketLayout: React.FC<SimpleBracketLayoutProps> = ({
  participants,
  onMatchUpdate
}) => {
  const [scores, setScores] = useState<{[key: string]: {score1: number, score2: number}}>({});

  // Parse participants from format "0001 ] Player Name"
  const parseParticipant = (participantStr: string) => {
    if (!participantStr || participantStr === 'TBD') {
      return { name: 'TBD', regNumber: '000' };
    }
    
    const match = participantStr.match(/^(\d+)\s*\]\s*(.+)$/);
    if (match) {
      return {
        name: match[2].trim(),
        regNumber: match[1]
      };
    }
    
    return { name: participantStr, regNumber: '000' };
  };

  // Ensure we have 8 participants
  const parsedParticipants = [...participants];
  while (parsedParticipants.length < 8) {
    parsedParticipants.push('000 ] TBD');
  }

  const p = parsedParticipants.map(parseParticipant);

  const handleScoreUpdate = (matchId: string, participantIndex: number, score: number) => {
    const currentScores = scores[matchId] || { score1: 0, score2: 0 };
    
    if (participantIndex === 0) {
      currentScores.score1 = score;
    } else {
      currentScores.score2 = score;
    }
    
    setScores(prev => ({
      ...prev,
      [matchId]: currentScores
    }));

    if (onMatchUpdate) {
      onMatchUpdate(matchId, currentScores.score1, currentScores.score2);
    }
  };

  const renderParticipant = (participant: any, matchId: string, participantIndex: number) => {
    const matchScores = scores[matchId] || { score1: 0, score2: 0 };
    const score = participantIndex === 0 ? matchScores.score1 : matchScores.score2;

    return (
      <div className="flex items-center justify-between p-3 bg-[#404040] text-white border-b border-gray-600 last:border-b-0 hover:bg-[#4a4a4a] transition-colors">
        <span className="text-sm flex-1">
          <span className="font-mono text-xs text-gray-300 mr-2">{participant.regNumber} ]</span>
          <span className="font-medium">{participant.name}</span>
        </span>
        <input 
          type="number" 
          className="w-12 h-8 ml-2 rounded bg-[#333] text-center text-sm border border-gray-600 text-white font-bold" 
          min={0}
          value={score || ''}
          onChange={(e) => handleScoreUpdate(matchId, participantIndex, parseInt(e.target.value) || 0)}
          disabled={participant.name === 'TBD'}
        />
      </div>
    );
  };

  const renderMatch = (id: number, participant1: any, participant2: any, showLine: boolean = false) => {
    const matchId = `match-${id}`;
    
    return (
      <div className="relative mb-6">
        {/* Simple connection line */}
        {showLine && (
          <div className="absolute left-full top-1/2 w-8 h-0.5 bg-gray-400 transform -translate-y-0.5 pointer-events-none z-0"></div>
        )}
        
        {/* Match container */}
        <div className="border border-gray-600 rounded-lg overflow-hidden bg-[#333333] relative z-10 w-60 shadow-lg">
          {/* Match ID */}
          <div className="absolute left-3 top-2 text-white text-sm font-bold z-20 bg-[#ff7f32] px-2 py-1 rounded text-xs">
            {id}
          </div>
          
          {/* Participants */}
          <div className="pt-8">
            {renderParticipant(participant1, matchId, 0)}
            {renderParticipant(participant2, matchId, 1)}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-full overflow-x-auto bg-[#333333] p-6 rounded-lg">
      <div className="min-w-[900px]">
        {/* Headers */}
        <div className="grid grid-cols-3 gap-8 mb-6 border-b border-gray-700 pb-4">
          <div className="text-center">
            <h3 className="font-bold text-[#ff7f32] text-lg">Quarter Finals</h3>
          </div>
          <div className="text-center">
            <h3 className="font-bold text-[#ff7f32] text-lg">Semi Finals</h3>
          </div>
          <div className="text-center">
            <h3 className="font-bold text-[#ff7f32] text-lg">Final</h3>
          </div>
        </div>

        {/* Bracket Layout */}
        <div className="grid grid-cols-3 gap-8">
          {/* Quarter Finals */}
          <div className="space-y-6">
            {renderMatch(1, p[0], p[1], true)}
            {renderMatch(2, p[2], p[3], true)}
            {renderMatch(3, p[4], p[5], true)}
            {renderMatch(4, p[6], p[7], true)}
          </div>

          {/* Semi Finals */}
          <div className="space-y-12 pt-12">
            {renderMatch(5, {name: 'Winner QF1', regNumber: '000'}, {name: 'Winner QF2', regNumber: '000'}, true)}
            {renderMatch(6, {name: 'Winner QF3', regNumber: '000'}, {name: 'Winner QF4', regNumber: '000'}, true)}
          </div>

          {/* Final */}
          <div className="pt-24">
            {renderMatch(7, {name: 'Winner SF1', regNumber: '000'}, {name: 'Winner SF2', regNumber: '000'}, false)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleBracketLayout;

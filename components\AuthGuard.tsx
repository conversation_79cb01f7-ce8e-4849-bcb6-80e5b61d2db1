"use client";

import { useSession } from "next-auth/react";
import { useRouter, usePathname } from "next/navigation";
import { useEffect } from "react";

interface AuthGuardProps {
  children: React.ReactNode;
}

export default function AuthGuard({ children }: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Skip jika masih loading
    if (status === "loading") return;

    // If user is not authenticated and trying to access protected route
    if (status === "unauthenticated") {
      const callbackUrl = encodeURIComponent(pathname);
      router.replace(`/login?callbackUrl=${callbackUrl}`);
      return;
    }

    // If user is authenticated and trying to access login/signup
    if (status === "authenticated" && (pathname === '/login' || pathname === '/signup')) {
      router.replace('/user-dashboard');
      return;
    }

    // If user tries to access root and is authenticated, redirect to dashboard
    if (status === "authenticated" && pathname === '/') {
      router.replace('/user-dashboard');
      return;
    }

  }, [status, pathname, router]);

  // Show loading screen while checking authentication
  if (status === "loading") {
    return (
      <div className="min-h-screen bg-[#333333] flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff7f32]"></div>
      </div>
    );
  }

  // If user is not authenticated, show nothing (redirect will happen)
  if (status === "unauthenticated") {
    return null;
  }

  // If everything is fine, render children
  return <>{children}</>;
}



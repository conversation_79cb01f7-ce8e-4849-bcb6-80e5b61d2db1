/**
 * Stage 1 SE/DE Implementation Testing Suite
 * Comprehensive testing for all Stage 1 scenarios
 * Created by: Panglima's Tournament Management System
 */

import { GroupManager, Group, GroupResult, Participant } from './groupManagement';
import { DynamicBracketGenerator, BracketUtils } from './dynamicBracketAlgorithm';
import { BracketConfigurationBuilder } from './bracketConfiguration';

// Test Data Generators
export class Stage1TestDataGenerator {
  static generateParticipants(count: number): Participant[] {
    const participants: Participant[] = [];
    for (let i = 1; i <= count; i++) {
      const paddedNumber = i.toString().padStart(3, '0');
      participants.push({
        noReg: paddedNumber,
        name: `Athlete ${paddedNumber}`,
        club: `Club ${Math.ceil(i / 4)}`
      });
    }
    return participants;
  }

  static generateTournamentSettings(
    typeOfStage: 'single' | 'two',
    format: 'single' | 'double',
    participants: number
  ) {
    return {
      id: `test-tournament-${Date.now()}`,
      name: `Test Tournament ${typeOfStage.toUpperCase()} ${format.toUpperCase()}`,
      status: 'ongoing',
      typeOfStage,
      groupStageFormat: typeOfStage === 'two' ? format : '',
      groupAdvance: 2,
      finalStageFormat: typeOfStage === 'two' ? format : '',
      matchFormat: 'best-of-3',
      maxTeams: participants,
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    };
  }
}

// Group Management Testing
export class GroupManagementTester {
  static testGroupCreation(): void {
    console.log('🧪 Testing Group Creation...');
    
    const testCases = [
      { participants: 8, expected: 1 },
      { participants: 16, expected: 1 },
      { participants: 24, expected: 3 }, // 8+8+8
      { participants: 32, expected: 1 },
      { participants: 40, expected: 5 }, // 8x5
      { participants: 64, expected: 2 }, // 32+32
    ];

    testCases.forEach(({ participants, expected }) => {
      try {
        const manager = new GroupManager(`test-${participants}`);
        const testParticipants = Stage1TestDataGenerator.generateParticipants(participants);
        const groups = manager.createSEDEGroups(testParticipants, 'single', 2);
        
        console.log(`✅ ${participants} participants → ${groups.length} groups (expected: ${expected})`);
        
        // Validate group sizes
        groups.forEach((group, index) => {
          const size = group.participants.length;
          if (size < 8 || size > 33) {
            console.error(`❌ Group ${index + 1} has invalid size: ${size}`);
          }
        });
        
        // Validate total participants
        const totalInGroups = groups.reduce((sum, g) => sum + g.participants.length, 0);
        if (totalInGroups !== participants) {
          console.error(`❌ Participant count mismatch: ${totalInGroups} vs ${participants}`);
        }
        
      } catch (error) {
        console.error(`❌ Failed for ${participants} participants:`, error);
      }
    });
  }

  static testGroupDistribution(): void {
    console.log('\n🎯 Testing Group Distribution...');
    
    const edgeCases = [
      { participants: 7, shouldFail: true },  // Below minimum
      { participants: 8, shouldFail: false }, // Minimum
      { participants: 25, shouldFail: false }, // Odd number
      { participants: 100, shouldFail: false }, // Large number
    ];

    edgeCases.forEach(({ participants, shouldFail }) => {
      try {
        const manager = new GroupManager(`test-dist-${participants}`);
        const testParticipants = Stage1TestDataGenerator.generateParticipants(participants);
        const groups = manager.createSEDEGroups(testParticipants, 'single', 2);
        
        if (shouldFail) {
          console.error(`❌ Expected failure for ${participants} participants but succeeded`);
        } else {
          console.log(`✅ Successfully distributed ${participants} participants into ${groups.length} groups`);
        }
        
      } catch (error) {
        if (shouldFail) {
          console.log(`✅ Expected failure for ${participants} participants: ${error}`);
        } else {
          console.error(`❌ Unexpected failure for ${participants} participants:`, error);
        }
      }
    });
  }

  static testGroupResults(): void {
    console.log('\n📊 Testing Group Results...');
    
    const manager = new GroupManager('test-results');
    const participants = Stage1TestDataGenerator.generateParticipants(16);
    const groups = manager.createSEDEGroups(participants, 'single', 2);
    
    // Simulate results for first group
    const mockResults: GroupResult[] = groups[0].participants.map((participant, index) => ({
      participant,
      rank: index + 1,
      points: 10 - index * 2,
      wins: 5 - index,
      losses: index,
      eliminated: index >= 2 // Only top 2 advance
    }));
    
    manager.updateGroupResults(0, mockResults);
    
    const winners = manager.getGroupWinners();
    console.log(`✅ Group results updated, ${winners.length} winners identified`);
    
    const stats = manager.getGroupStatistics();
    console.log(`✅ Statistics: ${stats.completedGroups}/${stats.totalGroups} groups completed`);
  }
}

// Bracket Integration Testing
export class BracketIntegrationTester {
  static testBracketGeneration(): void {
    console.log('\n🏆 Testing Bracket Generation...');
    
    const groupSizes = [8, 9, 16, 17, 32, 33];
    
    groupSizes.forEach(size => {
      try {
        const config = BracketUtils.getDefaultConfig(size);
        const generator = new DynamicBracketGenerator(config);
        const structure = generator.getBracketStructure();
        
        const totalMatches = structure.reduce((sum, round) => sum + round.totalMatches, 0);
        const expectedMatches = size - 1; // SE formula
        
        if (totalMatches === expectedMatches) {
          console.log(`✅ ${size} participants → ${structure.length} rounds, ${totalMatches} matches`);
        } else {
          console.error(`❌ ${size} participants: expected ${expectedMatches} matches, got ${totalMatches}`);
        }
        
      } catch (error) {
        console.error(`❌ Bracket generation failed for ${size} participants:`, error);
      }
    });
  }

  static testBracketConfiguration(): void {
    console.log('\n⚙️ Testing Bracket Configuration...');
    
    const formats: Array<'single' | 'double'> = ['single', 'double'];
    const sizes = [8, 16, 32];
    
    formats.forEach(format => {
      sizes.forEach(size => {
        try {
          const config = new BracketConfigurationBuilder()
            .setParticipants(size)
            .setEliminationType(format)
            .setTheme('dark')
            .setSpacing('normal')
            .setSizing('medium')
            .build();
          
          console.log(`✅ ${format.toUpperCase()} config for ${size} participants created`);
          
          // Validate configuration
          const errors = require('./bracketConfiguration').BracketConfigUtils.validateConfig(config);
          if (errors.length > 0) {
            console.error(`❌ Configuration errors:`, errors);
          }
          
        } catch (error) {
          console.error(`❌ Configuration failed for ${format} ${size}:`, error);
        }
      });
    });
  }
}

// Tournament Flow Testing
export class TournamentFlowTester {
  static testSingleStageTournament(): void {
    console.log('\n🎮 Testing Single Stage Tournament...');
    
    const participants = Stage1TestDataGenerator.generateParticipants(16);
    const settings = Stage1TestDataGenerator.generateTournamentSettings('single', 'single', 16);
    
    try {
      // Test direct SE tournament
      const config = BracketUtils.getDefaultConfig(16);
      const generator = new DynamicBracketGenerator(config);
      const structure = generator.getBracketStructure();
      
      console.log(`✅ Single stage SE tournament: ${structure.length} rounds`);
      console.log(`   Participants: ${participants.length}`);
      console.log(`   Total matches: ${structure.reduce((sum, r) => sum + r.totalMatches, 0)}`);
      
    } catch (error) {
      console.error('❌ Single stage tournament test failed:', error);
    }
  }

  static testTwoStageTournament(): void {
    console.log('\n🎯 Testing Two Stage Tournament...');
    
    const participants = Stage1TestDataGenerator.generateParticipants(32);
    const settings = Stage1TestDataGenerator.generateTournamentSettings('two', 'single', 32);
    
    try {
      // Stage 1: Group SE
      const manager = new GroupManager('test-two-stage');
      const groups = manager.createSEDEGroups(participants, 'single', 2);
      
      console.log(`✅ Stage 1: ${groups.length} groups created`);
      
      // Simulate group completion
      groups.forEach((group, index) => {
        const mockResults: GroupResult[] = group.participants.map((participant, pIndex) => ({
          participant,
          rank: pIndex + 1,
          points: 10 - pIndex * 2,
          wins: 5 - pIndex,
          losses: pIndex,
          eliminated: pIndex >= 2
        }));
        
        manager.updateGroupResults(index, mockResults);
      });
      
      // Stage 2: Final bracket
      const winners = manager.getGroupWinners();
      const finalConfig = BracketUtils.getDefaultConfig(winners.length);
      const finalGenerator = new DynamicBracketGenerator(finalConfig);
      const finalStructure = finalGenerator.getBracketStructure();
      
      console.log(`✅ Stage 2: ${winners.length} winners → ${finalStructure.length} rounds`);
      
    } catch (error) {
      console.error('❌ Two stage tournament test failed:', error);
    }
  }

  static testBYEHandling(): void {
    console.log('\n🔄 Testing BYE Handling...');
    
    const oddCounts = [9, 17, 25, 33];
    
    oddCounts.forEach(count => {
      try {
        const participants = Stage1TestDataGenerator.generateParticipants(count);
        const manager = new GroupManager(`test-bye-${count}`);
        const groups = manager.createSEDEGroups(participants, 'single', 2);
        
        // Check if BYEs are properly handled
        groups.forEach((group, index) => {
          const size = group.participants.length;
          const nextPowerOf2 = Math.pow(2, Math.ceil(Math.log2(size)));
          const byeCount = nextPowerOf2 - size;
          
          if (byeCount > 1) {
            console.warn(`⚠️  Group ${index + 1} has ${byeCount} BYEs (size: ${size})`);
          } else {
            console.log(`✅ Group ${index + 1} BYE handling: ${byeCount} BYE (size: ${size})`);
          }
        });
        
      } catch (error) {
        console.error(`❌ BYE handling failed for ${count} participants:`, error);
      }
    });
  }
}

// Performance Testing
export class Stage1PerformanceTester {
  static testLargeGroupCreation(): void {
    console.log('\n⚡ Testing Large Group Creation Performance...');
    
    const largeCounts = [100, 200, 500, 1000];
    
    largeCounts.forEach(count => {
      const startTime = performance.now();
      
      try {
        const participants = Stage1TestDataGenerator.generateParticipants(count);
        const manager = new GroupManager(`test-perf-${count}`);
        const groups = manager.createSEDEGroups(participants, 'single', 2);
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        console.log(`✅ ${count} participants → ${groups.length} groups in ${duration.toFixed(2)}ms`);
        
        if (duration > 1000) {
          console.warn(`⚠️  Performance warning: ${count} participants took ${duration.toFixed(2)}ms`);
        }
        
      } catch (error) {
        console.error(`❌ Performance test failed for ${count} participants:`, error);
      }
    });
  }

  static testMemoryUsage(): void {
    console.log('\n🧠 Testing Memory Usage...');
    
    if ((performance as any).memory) {
      const memBefore = (performance as any).memory.usedJSHeapSize;
      
      // Create multiple large tournaments
      for (let i = 0; i < 10; i++) {
        const participants = Stage1TestDataGenerator.generateParticipants(100);
        const manager = new GroupManager(`test-memory-${i}`);
        manager.createSEDEGroups(participants, 'single', 2);
      }
      
      const memAfter = (performance as any).memory.usedJSHeapSize;
      const memUsed = memAfter - memBefore;
      
      console.log(`✅ Memory usage: ${(memUsed / 1024 / 1024).toFixed(2)} MB for 10 tournaments`);
    } else {
      console.log('ℹ️  Memory testing not available in this environment');
    }
  }
}

// Comprehensive Test Suite
export class Stage1TestSuite {
  static runAllTests(): void {
    console.log('🚀 Starting Stage 1 SE/DE Implementation Test Suite...\n');
    
    // Group Management Tests
    console.log('📋 GROUP MANAGEMENT TESTS');
    console.log('='.repeat(50));
    GroupManagementTester.testGroupCreation();
    GroupManagementTester.testGroupDistribution();
    GroupManagementTester.testGroupResults();
    
    // Bracket Integration Tests
    console.log('\n🏆 BRACKET INTEGRATION TESTS');
    console.log('='.repeat(50));
    BracketIntegrationTester.testBracketGeneration();
    BracketIntegrationTester.testBracketConfiguration();
    
    // Tournament Flow Tests
    console.log('\n🎮 TOURNAMENT FLOW TESTS');
    console.log('='.repeat(50));
    TournamentFlowTester.testSingleStageTournament();
    TournamentFlowTester.testTwoStageTournament();
    TournamentFlowTester.testBYEHandling();
    
    // Performance Tests
    console.log('\n⚡ PERFORMANCE TESTS');
    console.log('='.repeat(50));
    Stage1PerformanceTester.testLargeGroupCreation();
    Stage1PerformanceTester.testMemoryUsage();
    
    console.log('\n✨ All Stage 1 tests completed!');
  }

  static runQuickTest(): void {
    console.log('⚡ Running Quick Stage 1 Test...\n');
    
    GroupManagementTester.testGroupCreation();
    BracketIntegrationTester.testBracketGeneration();
    TournamentFlowTester.testSingleStageTournament();
    
    console.log('\n✅ Quick test completed!');
  }
}

// Export test runner
export const runStage1Tests = () => {
  Stage1TestSuite.runAllTests();
};

export const runQuickStage1Test = () => {
  Stage1TestSuite.runQuickTest();
};

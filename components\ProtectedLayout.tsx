"use client";

import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import DynamicSidebar from "@/components/DynamicSidebar";
import AuthGuard from "@/components/AuthGuard";

interface ProtectedLayoutProps {
  children: React.ReactNode;
}

// Routes yang tidak memerlukan sidebar (public routes)
const PUBLIC_ROUTES = [
  '/login',
  '/signup',
  '/error',
  '/'
];

export default function ProtectedLayout({ children }: ProtectedLayoutProps) {
  const pathname = usePathname();

  // Check if current route is public
  const isPublicRoute = PUBLIC_ROUTES.some(route => {
    if (route === '/') return pathname === '/';
    return pathname.startsWith(route);
  });

  // Check if route starts with /api
  const isApiRoute = pathname.startsWith('/api');

  // If it's an API route, just return children without any layout
  if (isApiRoute) {
    return <>{children}</>;
  }

  // If it's a public route, don't show sidebar and don't use AuthGuard
  if (isPublicRoute) {
    return (
      <div className="min-h-screen bg-[#333333]">
        {children}
      </div>
    );
  }

  // For protected routes, show sidebar (temporarily without auth guard for testing)
  return (
    <div className="flex min-h-screen">
      <DynamicSidebar />
      <main className="flex-1 bg-[#1a1a1a]">
        {children}
      </main>
    </div>
  );
}

// Component for public pages (login, signup, etc.)
export function PublicLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="min-h-screen bg-[#333333]">
      {children}
    </div>
  );
}

// Component for authenticated pages with sidebar
export function AuthenticatedLayout({ children }: { children: React.ReactNode }) {
  return (
    <AuthGuard>
      <div className="flex min-h-screen">
        <DynamicSidebar />
        <main className="flex-1 bg-[#1a1a1a]">
          {children}
        </main>
      </div>
    </AuthGuard>
  );
}

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(req: NextRequest) {
  try {
    const tournaments = await prisma.tournament.findMany({
      include: {
        organizer: true,
        participants: true,
      },
      orderBy: { startDate: "asc" },
    });
    return NextResponse.json({ tournaments });
  } catch (err) {
    return NextResponse.json({ error: "Failed to fetch tournaments" }, { status: 500 });
  }
} 
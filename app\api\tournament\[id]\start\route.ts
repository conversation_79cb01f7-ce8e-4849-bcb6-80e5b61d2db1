import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  const { id } = params;
  try {
    const tournament = await prisma.tournament.update({
      where: { id },
      data: { status: "ongoing" },
    });
    return NextResponse.json({ success: true, tournament });
  } catch (err) {
    return NextResponse.json({ error: "Failed to start stage" }, { status: 500 });
  }
} 
"use client"

import React, { useEffect, useState } from 'react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Eye, EyeOff } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (...args: any[]) => any;
    lastAutoTable: any;
  }
}

type Participant = { name: string; club: string; noReg?: string };

type Group = Participant[];

// Tambahkan type untuk skor match
interface MatchScore {
  groupIdx: number;
  roundIdx: number;
  matchIdx: number;
  score1: number;
  score2: number;
}

function generateRoundRobinPairings(participants: Participant[]): Participant[][][] {
  const n = participants.length;
  if (n < 2) return [];
  let arr = [...participants];
  if (n % 2 === 1) arr.push({ name: 'BYE', club: '-' });
  const rounds: Participant[][][] = [];
  for (let round = 0; round < arr.length - 1; round++) {
    const matches: Participant[][] = [];
    for (let i = 0; i < arr.length / 2; i++) {
      const p1 = arr[i];
      const p2 = arr[arr.length - 1 - i];
      if (p1.name !== 'BYE' && p2.name !== 'BYE') matches.push([p1, p2]);
    }
    // Rotasi peserta kecuali yang pertama
    arr.splice(1, 0, arr.pop()!);
    rounds.push(matches);
  }
  return rounds;
}

export default function BracketPage() {
  const { data: session } = useSession();
  const userName = session?.user?.name || "Unknown User";
  const [groups, setGroups] = useState<Group[]>([]);
  const [activeTabs, setActiveTabs] = useState<{ [key: number]: 'standings' | 'matches' }>({});
  const [pairings, setPairings] = useState<Participant[][][][]>([]);
  const [matchScores, setMatchScores] = useState<MatchScore[]>([]);
  const [standings, setStandings] = useState<{ [groupIdx: number]: any[] }>({});
  const router = useRouter();
  const params = useParams();
  const [tournamentStatus, setTournamentStatus] = useState<string>("upcoming");
  const [isLocked, setIsLocked] = useState(true);
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [passwordError, setPasswordError] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [showFinalModal, setShowFinalModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [finalStageStarted, setFinalStageStarted] = useState(false);
  const [finalStageError, setFinalStageError] = useState<string>("");

  // Load data awal
  useEffect(() => {
    const data = localStorage.getItem('tournamentGroups');
    if (data) {
      const parsed: Group[] = JSON.parse(data);
      setGroups(parsed);
      setPairings(parsed.map((g) => generateRoundRobinPairings(g)));
      const tabs: { [key: number]: 'standings' | 'matches' } = {};
      parsed.forEach((_, i) => (tabs[i] = 'matches'));
      setActiveTabs(tabs);
    }
    // Load skor match dari localStorage (unique per tournament)
    const scores = localStorage.getItem(`matchScores_${params.id}`);
    if (scores) setMatchScores(JSON.parse(scores));
  }, []);

  // Simpan skor match ke localStorage setiap kali berubah (unique per tournament)
  useEffect(() => {
    localStorage.setItem(`matchScores_${params.id}`, JSON.stringify(matchScores));
  }, [matchScores, params.id]);

  // Hitung standings setiap kali skor match berubah
  useEffect(() => {
    const newStandings: { [groupIdx: number]: any[] } = {};
    groups.forEach((group, groupIdx) => {
      // Inisialisasi statistik peserta
      const stats = group.map((p, idx) => ({
        idx,
        participant: p,
        matchW: 0,
        matchL: 0,
        scoreW: 0,
        scoreL: 0,
        matchPoin: 0,
        diff: 0,
        rank: 0,
      }));
      // Pairing untuk grup ini
      const groupPairings = pairings[groupIdx] || [];
      // Loop seluruh match di grup ini
      groupPairings.forEach((round, roundIdx) => {
        round.forEach((match, matchIdx) => {
          const score = matchScores.find(
            (s) => s.groupIdx === groupIdx && s.roundIdx === roundIdx && s.matchIdx === matchIdx
          );
          if (!score) return;
          const [p1, p2] = match;
          const idx1 = group.findIndex((x) => x.name === p1.name && x.club === p1.club);
          const idx2 = group.findIndex((x) => x.name === p2.name && x.club === p2.club);
          if (idx1 === -1 || idx2 === -1) return;
          // Update skor
          stats[idx1].scoreW += score.score1;
          stats[idx1].scoreL += score.score2;
          stats[idx2].scoreW += score.score2;
          stats[idx2].scoreL += score.score1;
          // Menang/kalah
          if (score.score1 > score.score2) {
            stats[idx1].matchW += 1;
            stats[idx1].matchPoin += 1;
            stats[idx2].matchL += 1;
          } else if (score.score2 > score.score1) {
            stats[idx2].matchW += 1;
            stats[idx2].matchPoin += 1;
            stats[idx1].matchL += 1;
          }
          // Tidak ada draw
        });
      });
      // Hitung diff
      stats.forEach((s) => {
        s.diff = s.scoreW - s.scoreL;
      });
      // Urutkan sesuai aturan rank
      stats.sort((a, b) =>
        b.matchPoin - a.matchPoin ||
        b.diff - a.diff ||
        b.scoreW - a.scoreW ||
        a.idx - b.idx
      );
      // Tambahkan rank
      stats.forEach((s, i) => (s.rank = i + 1));
      newStandings[groupIdx] = stats;
    });
    setStandings(newStandings);
  }, [groups, pairings, matchScores]);

  // Fetch status tournament by id
  useEffect(() => {
    async function fetchStatus() {
      const res = await fetch(`/api/tournament/list`);
      const data = await res.json();
      const t = data.tournaments?.find((t: any) => t.id === params.id);
      if (t) setTournamentStatus(t.status);
    }
    fetchStatus();
  }, [params.id]);

  // Handler input skor
  const handleScoreChange = (
    groupIdx: number,
    roundIdx: number,
    matchIdx: number,
    player: 1 | 2,
    value: number
  ) => {
    setMatchScores((prev) => {
      const next = [...prev];
      let entry = next.find(
        (s) => s.groupIdx === groupIdx && s.roundIdx === roundIdx && s.matchIdx === matchIdx
      );
      if (!entry) {
        entry = { groupIdx, roundIdx, matchIdx, score1: 0, score2: 0 };
        next.push(entry);
      }
      if (player === 1) entry.score1 = value;
      else entry.score2 = value;
      return next;
    });
  };

  // Helper untuk ambil info turnamen dari localStorage
  function getTournamentInfo() {
    if (typeof window === 'undefined') return {};
    try {
      const info = localStorage.getItem('tournamentInfo');
      if (info) return JSON.parse(info);
    } catch {}
    return {};
  }

  // Fungsi export PDF
  const exportStandingsToPDF = () => {
    const doc = new jsPDF();
    let y = 10;
    groups.forEach((group, groupIdx) => {
      const groupName = `Group ${String(groupIdx + 1).padStart(4, '0')}`;
      doc.text(groupName, 10, y);
      y += 6;
      const data = (standings[groupIdx] || group.map((p, i) => ({
        participant: p, matchW: 0, matchL: 0, scoreW: 0, scoreL: 0, matchPoin: 0, diff: 0, rank: i + 1
      }))).map(s => [
        (s.participant.noReg ? `${s.participant.noReg} ] ` : '') + s.participant.name + ` (${s.participant.club})`,
        `${s.matchW} / ${s.matchL}`,
        `${s.scoreW} / ${s.scoreL}`,
        s.matchPoin,
        s.diff,
        s.rank
      ]);
      autoTable(doc, {
        head: [['Participant', 'Match W/L', 'Score W/L', 'Match Poin', 'Diff', 'Rank']],
        body: data,
        startY: y,
        theme: 'grid',
        headStyles: { fillColor: [204, 121, 0], textColor: 255 }, // #CC7900, white
        styles: { fontSize: 8 },
        margin: { left: 10, right: 10 },
        didDrawPage: (data) => { if (data && data.cursor && typeof data.cursor.y === 'number') { y = data.cursor.y + 10; } }
      });
      y = doc.lastAutoTable ? doc.lastAutoTable.finalY + 10 : y + 10;
      if (y > 260) { doc.addPage(); y = 10; }
    });
    // Tambahkan info export di akhir file
    const now = new Date();
    const dateStr = now.toLocaleDateString('en-GB', { year: 'numeric', month: 'long', day: 'numeric' });
    const timeStr = now.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit', second: '2-digit' });
    const info = getTournamentInfo();
    y += 10;
    doc.setFontSize(9);
    doc.text("EXPORT DATA INFORMATION:", 10, y);
    y += 7;
    doc.text(`Tournament Name: ${info.name || 'Unknown Tournament'}`, 10, y);
    y += 7;
    doc.text(`Status: ${info.status || '-'}`, 10, y);
    y += 7;
    doc.text(`Type: ${info.typeOfStage || '-'}`, 10, y);
    y += 7;
    doc.text(`Format: ${info.format || '-'}`, 10, y);
    y += 7;
    doc.text(`Group Stage Format: ${info.groupStageFormat || '-'}`, 10, y);
    y += 7;
    doc.text(`Final Stage Format: ${info.finalStageFormat || '-'}`, 10, y);
    y += 7;
    doc.text(`User Name: ${userName}`, 10, y);
    y += 7;
    doc.text(`Export Date: ${dateStr}`, 10, y);
    y += 7;
    doc.text(`Export Time: ${timeStr}`, 10, y);
    doc.setFontSize(8);
    doc.save('standings_all_groups.pdf');
  };

  // Handler start stage
  async function handleStartStage() {
    setShowModal(false);
    await fetch(`/api/tournament/${params.id}/start`, { method: "PATCH" });
    setTournamentStatus("ongoing");
    setIsLocked(false); // Automatically unlock when stage starts
    router.refresh();
  }

  async function handleUnlock() {
    setPasswordError("");
    try {
      const res = await fetch('/api/user/verify-password', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password }),
      });
      const data = await res.json();
      if (res.ok && data.success) {
        setIsLocked(false);
        setShowPasswordDialog(false);
        setPassword("");
      } else {
        setPasswordError(data.error || "An unknown error occurred.");
      }
    } catch (error) {
      setPasswordError("Failed to connect to the server.");
    }
  }

  // Handler untuk export dan proceed ke final stage
  function handleExportAndProceed() {
    // Export PDF terlebih dahulu
    exportStandingsToPDF();

    // Tutup export modal
    setShowExportModal(false);

    // Tampilkan final modal untuk konfirmasi proceed
    setShowFinalModal(true);
  }

  // Handler untuk final proceed (setelah export)
  function handleFinalProceed() {
    setShowFinalModal(false);

    // Save standings data to localStorage for final stage
    try {
      // Convert standings to the format expected by final stage
      const standingsForFinal: { [groupName: string]: any[] } = {};

      Object.keys(standings).forEach(groupIdx => {
        const groupName = `Group ${String(parseInt(groupIdx) + 1).padStart(4, '0')}`;
        const groupStandings = standings[parseInt(groupIdx)] || [];

        // Map standings to final stage format
        standingsForFinal[groupName] = groupStandings.map(s => ({
          noReg: s.participant.noReg || '',
          name: s.participant.name,
          club: s.participant.club,
          points: s.matchPoin, // Use matchPoin as points
          wins: s.matchW,
          losses: s.matchL,
          rank: s.rank
        }));
      });

      // Save to localStorage with the key expected by final stage
      localStorage.setItem(`standings_${params.id}`, JSON.stringify(standingsForFinal));
      console.log("Standings saved to localStorage for final stage:", standingsForFinal);
    } catch (err) {
      console.error("Failed to save standings to localStorage:", err);
    }

    // Update status tournament ke finalstage
    fetch(`/api/tournament/${params.id}/finalstage`, {
      method: "PATCH",
    }).then(res => {
      if (res.ok) {
        console.log("Tournament status updated to finalstage");
        // Navigate to final stage page
        window.location.href = `/tournaments/${params.id}/final`;
      }
    }).catch(err => {
      console.error("Failed to update tournament status:", err);
      // Even if API fails, still navigate to final stage
      window.location.href = `/tournaments/${params.id}/final`;
    });
  }

  // Handler klik tombol Proceed to Final Stage
  function handleClickProceedFinalStage() {
    // Validasi skor semua match group stage, matchNo harus identik dengan UI
    const groupErrors: { [groupIdx: number]: { groupName: string, matches: { matchNo: number, msg: string }[] } } = {};
    const roundsPerRow = 2;
    groups.forEach((group, groupIdx) => {
      const groupPairings = pairings[groupIdx] || [];
      // Bagi ke rows seperti di UI
      const rows = [];
      for (let i = 0; i < groupPairings.length; i += roundsPerRow) {
        rows.push(groupPairings.slice(i, i + roundsPerRow));
      }
      let matchNo = 1;
      rows.forEach((roundRow, rowIdx) => {
        const maxMatch = Math.max(...roundRow.map(round => round.length));
        for (let mIdx = 0; mIdx < maxMatch; mIdx++) {
          roundRow.forEach((round, rIdx) => {
            const match = round[mIdx];
            if (match) {
              const roundIdx = rowIdx * roundsPerRow + rIdx;
              const matchIdx = mIdx;
              const score = matchScores.find(
                (s) => s.groupIdx === groupIdx && s.roundIdx === roundIdx && s.matchIdx === matchIdx
              );
              let msg = "";
              if (!score || (score.score1 === 0 && score.score2 === 0)) {
                msg = `Match ${matchNo} (score 0-0)`;
              } else if (score.score1 < 7 && score.score2 < 7) {
                msg = `Match ${matchNo} (no winner >=7)`;
              }
              if (msg) {
                if (!groupErrors[groupIdx]) {
                  groupErrors[groupIdx] = { groupName: `GROUP ${String(groupIdx + 1).padStart(4, '0')}`, matches: [] };
                }
                groupErrors[groupIdx].matches.push({ matchNo, msg });
              }
              matchNo++;
            }
          });
        }
      });
    });
    if (Object.keys(groupErrors).length > 0) {
      // Format pesan error: hanya daftar GROUP 0001 | GROUP 0002 | ...
      const errorMsg =
        `Cannot proceed to final stage. The following matches are not valid:\n` +
        Object.values(groupErrors)
          .map(g => g.groupName)
          .join(' | ');
      setFinalStageError(errorMsg);
      return;
    }
    setFinalStageError("");
    // Tampilkan export modal setelah validasi berhasil
    setShowExportModal(true);
  }

  if (!groups.length) return <div className="text-white p-8">No group data found.</div>;

  return (
    <div className="min-h-screen bg-[#333333] text-white p-8">
      <div className="max-w-5xl mx-auto flex flex-col gap-1">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Tournament Bracket (RR)</h1>
        </div>
        <div className="mb-6 flex justify-end items-center gap-2">
          <button
            className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white px-4 py-2 rounded font-semibold text-sm"
            onClick={() => setShowModal(true)}
          >
            Start The Stage
          </button>
          <button
            onClick={() => setIsLocked(true)}
            className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded font-semibold text-sm"
          >
            Lock
          </button>
          <button
            onClick={() => setShowPasswordDialog(true)}
            className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white px-4 py-2 rounded font-semibold text-sm"
          >
            Open
          </button>
        </div>
        {showModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
            <div className="bg-[#222] p-6 rounded-lg shadow-lg max-w-sm w-full">
              <h2 className="text-lg font-bold mb-4">Are you sure?</h2>
              <p className="mb-6 text-sm text-gray-300">Once you start the stage, all tournament data will be locked and you can start entering scores. This action cannot be undone.</p>
              <div className="flex justify-end gap-2">
                <button onClick={() => setShowModal(false)} className="px-4 py-2 rounded bg-gray-600 text-white">Cancel</button>
                <button onClick={handleStartStage} className="px-4 py-2 rounded bg-[#ff7f32] text-white font-semibold">Yes, Start</button>
              </div>
            </div>
          </div>
        )}
        <div className="mb-6 text-sm text-gray-400">Total Groups: {groups.length}</div>
        {groups.map((group, groupIdx) => (
          <div key={groupIdx} className="mb-8 bg-[#222] rounded-lg p-4 text-xs">
            <div className="flex items-center justify-between gap-4 mb-4">
              <h2 className="font-bold text-sm text-[#ff7f32]">Group {String(groupIdx + 1).padStart(4, '0')}</h2>
              <div className="flex gap-2">
                <button
                  className={`px-3 py-1 rounded text-xs ${activeTabs[groupIdx] === 'standings' ? 'bg-[#ff7f32]' : 'bg-gray-700'} text-white`}
                  onClick={() => setActiveTabs((prev) => ({ ...prev, [groupIdx]: 'standings' }))}
                >
                  Standings
                </button>
                <button
                  className={`px-3 py-1 rounded text-xs ${activeTabs[groupIdx] === 'matches' ? 'bg-[#ff7f32]' : 'bg-gray-700'} text-white`}
                  onClick={() => setActiveTabs((prev) => ({ ...prev, [groupIdx]: 'matches' }))}
                >
                  Matches
                </button>
              </div>
            </div>
            {activeTabs[groupIdx] === 'standings' && (
              <div>
                <table className="w-full text-xs bg-[#222]">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="p-2 text-center border-r border-gray-700 text-xs">Participant</th>
                      <th className="p-2 text-center border-r border-gray-700 text-xs">Match W/L</th>
                      <th className="p-2 text-center border-r border-gray-700 text-xs">Score W/L</th>
                      <th className="p-2 text-center border-r border-gray-700 text-xs">Match Poin</th>
                      <th className="p-2 text-center border-r border-gray-700 text-xs">Diff</th>
                      <th className="p-2 text-center text-xs">Rank</th>
                    </tr>
                  </thead>
                  <tbody>
                    {(standings[groupIdx] || groups[groupIdx].map((p, i) => ({ participant: p, matchW: 0, matchL: 0, scoreW: 0, scoreL: 0, matchPoin: 0, diff: 0, rank: i + 1 }))).map((s, i) => (
                      <tr key={i} className="border-b border-gray-700">
                        <td className="p-2 text-left border-r border-gray-700 text-xs">{s.participant.noReg ? `${s.participant.noReg} ] ` : ''}{s.participant.name} <span className="font-normal text-gray-300">({s.participant.club})</span></td>
                        <td className="p-2 text-center border-r border-gray-700 text-xs">{s.matchW} / {s.matchL}</td>
                        <td className="p-2 text-center border-r border-gray-700 text-xs">{s.scoreW} / {s.scoreL}</td>
                        <td className="p-2 text-center border-r border-gray-700 text-xs">{s.matchPoin}</td>
                        <td className="p-2 text-center border-r border-gray-700 text-xs">{s.diff}</td>
                        <td className="p-2 text-center text-xs">{('rank' in s ? s.rank : i + 1)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            {activeTabs[groupIdx] === 'matches' && (
              <div>
                {pairings[groupIdx] && pairings[groupIdx].length > 0 ? (
                  (() => {
                    let matchNo = 1;
                    const roundsPerRow = 2;
                    const rows = [];
                    for (let i = 0; i < pairings[groupIdx].length; i += roundsPerRow) {
                      rows.push(pairings[groupIdx].slice(i, i + roundsPerRow));
                    }
                    return (
                      <div className="flex flex-col gap-4">
                        {rows.map((roundRow, rowIdx) => {
                          const maxMatch = Math.max(...roundRow.map(round => round.length));
                          return (
                            <div key={rowIdx} className="flex flex-row gap-4">
                              {roundRow.map((round: Participant[][], rIdx: number) => (
                                <div key={rIdx} className="min-w-[220px] flex-1 border-r border-gray-700 last:border-r-0 pr-4">
                                  <div className="font-semibold mb-2">Round {rowIdx * roundsPerRow + rIdx + 1}</div>
                                  <div className="flex flex-col gap-2">
                                    {Array.from({ length: maxMatch }).map((_, mIdx) => {
                                      const match = round[mIdx];
                                      if (match) {
                                        const score = matchScores.find(
                                          (s) => s.groupIdx === groupIdx && s.roundIdx === rowIdx * roundsPerRow + rIdx && s.matchIdx === mIdx
                                        ) || { score1: '', score2: '' };
                                        return (
                                          <div key={mIdx} className="flex flex-col items-stretch bg-[#444] rounded p-2 min-h-[64px] flex-1 justify-center relative">
                                            <div className="flex items-center justify-between">
                                              <span className="flex-1 text-left pl-4 pr-3">
                                                {match[0].noReg} ] {match[0].name} <span className="font-normal text-gray-300">({match[0].club})</span>
                                              </span>
                                              <input type="number" className="w-10 h-6 ml-2 rounded bg-[#333] text-center text-xs border border-gray-600" value={score.score1} min={0} onChange={e => handleScoreChange(groupIdx, rowIdx * roundsPerRow + rIdx, mIdx, 1, Number(e.target.value))} disabled={tournamentStatus !== "ongoing" || isLocked} />
                                            </div>
                                            <div className="w-full border-t border-gray-500 my-1 relative">
                                              <span className="absolute left-0 -top-3 w-4 text-[10px] text-gray-300 bg-[#444] px-0.5 rounded text-center">{matchNo++}</span>
                                            </div>
                                            <div className="flex items-center justify-between">
                                              <span className="flex-1 text-left pl-4 pr-3">
                                                {match[1].noReg} ] {match[1].name} <span className="font-normal text-gray-300">({match[1].club})</span>
                                              </span>
                                              <input type="number" className="w-10 h-6 ml-2 rounded bg-[#333] text-center text-xs border border-gray-600" value={score.score2} min={0} onChange={e => handleScoreChange(groupIdx, rowIdx * roundsPerRow + rIdx, mIdx, 2, Number(e.target.value))} disabled={tournamentStatus !== "ongoing" || isLocked} />
                                            </div>
                                          </div>
                                        );
                                      } else {
                                        return <div key={mIdx} className="min-h-[64px] flex-1" />;
                                      }
                                    })}
                                  </div>
                                </div>
                              ))}
                              {roundRow.length < roundsPerRow && <div className="min-w-[220px] flex-1" />}
                            </div>
                          );
                        })}
                      </div>
                    );
                  })()
                ) : (
                  <div className="text-gray-400">No matches generated.</div>
                )}
              </div>
            )}
          </div>
        ))}
        {/* Tombol Export PDF dan Proceed to Final Stage */}
        {tournamentStatus === "ongoing" && (
          <div className="flex flex-col items-end mb-4 gap-2">
            {finalStageError && (
              <div className="border border-[#ff7f32] text-[#ff7f32] rounded px-6 py-4 text-sm whitespace-pre-line w-full text-left bg-transparent">
                <div className="font-bold uppercase mb-2">CANNOT PROCEED TO FINAL STAGE. THE FOLLOWING MATCHES ARE NOT VALID:</div>
                <div className="font-bold">{finalStageError.replace(/^Cannot proceed to final stage\. The following matches are not valid:\n?/i, "")}</div>
              </div>
            )}
            <div className="flex gap-2">
              <button
                onClick={exportStandingsToPDF}
                className="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded font-semibold text-sm"
              >
                Export to PDF
              </button>
              <button
                className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white px-4 py-2 rounded font-semibold text-sm"
                onClick={handleClickProceedFinalStage}
              >
                Proceed to Final Stage
              </button>
            </div>
          </div>
        )}
        {showExportModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
            <div className="bg-[#222] p-6 rounded-lg shadow-lg max-w-md w-full">
              <h2 className="text-lg font-bold mb-4">Export Required</h2>
              <p className="mb-6 text-sm text-gray-300">
                Anda harus melakukan export file PDF terlebih dahulu untuk melanjutkan ke tahap selanjutnya.
                File PDF akan berisi data standings dari semua grup yang telah selesai.
              </p>
              <div className="flex justify-end gap-2">
                <button
                  onClick={() => setShowExportModal(false)}
                  className="px-4 py-2 rounded bg-gray-600 hover:bg-gray-700 text-white"
                >
                  Cancel
                </button>
                <button
                  onClick={handleExportAndProceed}
                  className="px-4 py-2 rounded bg-[#ff7f32] hover:bg-[#e86a1f] text-white font-semibold"
                >
                  Save & Continue
                </button>
              </div>
            </div>
          </div>
        )}
        {showFinalModal && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
            <div className="bg-[#222] p-6 rounded-lg shadow-lg max-w-sm w-full">
              <h2 className="text-lg font-bold mb-4">Are you sure?</h2>
              <p className="mb-6 text-sm text-gray-300">Once you proceed to the final stage, all group stage data will be locked and you can start the final stage. This action cannot be undone.</p>
              <div className="flex justify-end gap-2">
                <button onClick={() => setShowFinalModal(false)} className="px-4 py-2 rounded bg-gray-600 text-white">Cancel</button>
                <button onClick={handleFinalProceed} className="px-4 py-2 rounded bg-[#ff7f32] text-white font-semibold">Yes, Proceed</button>
              </div>
            </div>
          </div>
        )}
        {/* Notifikasi sukses dummy */}
        {finalStageStarted && (
          <div className="mb-4 text-green-400 font-semibold text-center">Final stage started! (dummy)</div>
        )}
      </div>
     <AlertDialog open={showPasswordDialog} onOpenChange={setShowPasswordDialog}>
       <AlertDialogContent className="bg-[#2a2a2a] border-gray-700 text-white">
         <AlertDialogHeader>
           <AlertDialogTitle>Enter Password to Unlock</AlertDialogTitle>
           <AlertDialogDescription className="text-gray-400">
             Please enter your password to enable score editing.
           </AlertDialogDescription>
         </AlertDialogHeader>
         <div className="py-4 relative">
           <Input
             type={showPassword ? "text" : "password"}
             value={password}
             onChange={(e) => setPassword(e.target.value)}
             placeholder="Password"
             className="bg-[#333333] border-gray-600 pr-10"
           />
           <button
             type="button"
             onClick={() => setShowPassword(!showPassword)}
             className="absolute inset-y-0 right-0 flex items-center px-3 text-gray-400 hover:text-white"
           >
             {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
           </button>
         </div>
         {passwordError && <p className="text-red-500 text-sm mt-2 -mb-2">{passwordError}</p>}
         <AlertDialogFooter>
           <AlertDialogCancel onClick={() => { setPassword(""); setPasswordError(""); }} className="bg-gray-600 hover:bg-gray-700 border-0">Cancel</AlertDialogCancel>
           <AlertDialogAction onClick={handleUnlock} className="bg-[#ff7f32] hover:bg-[#e86a1f]">
             Unlock
           </AlertDialogAction>
         </AlertDialogFooter>
       </AlertDialogContent>
     </AlertDialog>
    </div>
  );
}

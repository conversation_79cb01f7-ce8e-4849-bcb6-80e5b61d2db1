"use client";
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useSearchParams, useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { formatDistanceToNow, format } from "date-fns"

interface TournamentListSectionProps {
  showHeader?: boolean;
  className?: string;
}

export default function TournamentListSection({ 
  showHeader = false, 
  className = "" 
}: TournamentListSectionProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const tab = searchParams.get("tab") || "upcoming"
  const [tournaments, setTournaments] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")

  useEffect(() => {
    async function fetchTournaments() {
      setLoading(true)
      const res = await fetch("/api/tournament/list")
      const data = await res.json()
      setTournaments(data.tournaments || [])
      setLoading(false)
    }
    fetchTournaments()
  }, [])

  const filterByStatus = (status: string) => {
    return tournaments.filter(tournament => {
      if (status === "upcoming") return tournament.status === "upcoming"
      if (status === "ongoing") return tournament.status === "ongoing"
      if (status === "completed") return tournament.status === "completed"
      return true
    })
  }

  const applyFilters = (tournamentList: any[]) => {
    return tournamentList.filter(tournament => {
      const matchesSearch = tournament.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === "all" || tournament.game === statusFilter
      const matchesType = typeFilter === "all" || tournament.type === typeFilter
      return matchesSearch && matchesStatus && matchesType
    })
  }

  return (
    <div className={`w-full ${className}`}>
      {showHeader && (
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <h1 className="text-3xl font-bold">Tournaments</h1>
          <Link href="/create-tournament">
            <Button className="bg-[#ff7f32] hover:bg-[#e86a1f] text-white">Create Tournament</Button>
          </Link>
        </div>
      )}

      {/* Filter Controls */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="flex-1">
          <Input
            placeholder="Search tournaments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="bg-[#2a2a2a] border-gray-700 text-white placeholder-gray-400"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full md:w-48 bg-[#2a2a2a] border-gray-700 text-white">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent className="bg-[#2a2a2a] border-gray-700">
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="Liga Domino Nasional">Liga Domino Nasional</SelectItem>
            <SelectItem value="Kejuaraan Berjenjang">Kejuaraan Berjenjang</SelectItem>
            <SelectItem value="Turnamen Terbuka">Turnamen Terbuka</SelectItem>
            <SelectItem value="Kompetisi Regional">Kompetisi Regional</SelectItem>
          </SelectContent>
        </Select>
        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-full md:w-48 bg-[#2a2a2a] border-gray-700 text-white">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent className="bg-[#2a2a2a] border-gray-700">
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="single stage">Single Stage</SelectItem>
            <SelectItem value="double stage">Double Stage</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Tournament Tabs */}
      <Tabs value={tab} className="w-full" onValueChange={v => router.push(`/user-dashboard?tab=${v}`)}>
        <TabsList className="bg-[#2a2a2a] border-b border-gray-700 w-full justify-start rounded-none mb-6">
          <TabsTrigger value="upcoming" className="data-[state=active]:bg-[#333333]">Upcoming</TabsTrigger>
          <TabsTrigger value="ongoing" className="data-[state=active]:bg-[#333333]">Ongoing</TabsTrigger>
          <TabsTrigger value="completed" className="data-[state=active]:bg-[#333333]">Completed</TabsTrigger>
        </TabsList>
        
        <TabsContent value="upcoming" className="mt-0">
          <div className="grid gap-4 max-h-96 overflow-y-auto">
            {loading ? (
              <div className="text-gray-400">Loading...</div>
            ) : applyFilters(filterByStatus("upcoming")).length === 0 ? (
              <div className="text-gray-400">No upcoming tournaments found.</div>
            ) : (
              applyFilters(filterByStatus("upcoming")).map((tournament) => (
                <Link
                  href={`/tournaments/${tournament.id}/bracket`}
                  key={tournament.id}
                  className="bg-[#2a2a2a] hover:bg-[#3a3a3a] rounded-lg p-4 transition-colors"
                >
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div>
                      <h3 className="font-bold text-lg mb-1">{tournament.name}</h3>
                      <div className="text-sm text-gray-400 mb-2">
                        <span className="inline-block bg-[#ff7f32] text-white text-xs px-2 py-0.5 rounded mr-2">
                          {tournament.type?.replace(/\b\w/g, (l: string) => l.toUpperCase()) || "-"}
                        </span>
                        <span>{tournament.participants?.length || 0} Participants</span>
                      </div>
                      <div className="text-sm text-gray-400">
                        Game: {tournament.game || "-"} • 
                        Start: {tournament.startDate ? format(new Date(tournament.startDate), "MMM dd, yyyy") : "-"}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-sm text-gray-400">
                        Organized by <span className="text-white">{tournament.organizer?.name || "-"}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="ongoing" className="mt-0">
          <div className="grid gap-4 max-h-96 overflow-y-auto">
            {loading ? (
              <div className="text-gray-400">Loading...</div>
            ) : applyFilters(filterByStatus("ongoing")).length === 0 ? (
              <div className="text-gray-400">No ongoing tournaments found.</div>
            ) : (
              applyFilters(filterByStatus("ongoing")).map((tournament) => (
                <div
                  key={tournament.id}
                  className="bg-[#2a2a2a] hover:bg-[#3a3a3a] rounded-lg p-4 transition-colors flex flex-col md:flex-row justify-between gap-4"
                >
                  <Link href={`/tournaments/${tournament.id}/bracket`} className="flex-1">
                    <h3 className="font-bold text-lg mb-1">{tournament.name}</h3>
                    <div className="text-sm text-gray-400 mb-2">
                      <span className="inline-block bg-green-600 text-white text-xs px-2 py-0.5 rounded mr-2">In Progress</span>
                      <span className="inline-block bg-[#ff7f32] text-white text-xs px-2 py-0.5 rounded mr-2">
                        {tournament.type?.replace(/\b\w/g, (l: string) => l.toUpperCase()) || "-"}
                      </span>
                      <span>{tournament.participants?.length || 0} Participants</span>
                    </div>
                    <div className="text-sm text-gray-400">
                      Game: {tournament.game || "-"} • Round info here
                    </div>
                  </Link>
                  <div className="flex items-center gap-2">
                    <div className="text-sm text-gray-400">
                      Organized by <span className="text-white">{tournament.organizer?.name || "-"}</span>
                    </div>
                    <Link href={`/tournaments/${tournament.id}/bracket`}>
                      <Button variant="outline" size="sm" className="border-gray-700 hover:bg-gray-700">
                        View Bracket
                      </Button>
                    </Link>
                  </div>
                </div>
              ))
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="completed" className="mt-0">
          <div className="grid gap-4 max-h-96 overflow-y-auto">
            {loading ? (
              <div className="text-gray-400">Loading...</div>
            ) : applyFilters(filterByStatus("completed")).length === 0 ? (
              <div className="text-gray-400">No completed tournaments found.</div>
            ) : (
              applyFilters(filterByStatus("completed")).map((tournament) => (
                <Link
                  href={`/tournaments/${tournament.id}`}
                  key={tournament.id}
                  className="bg-[#2a2a2a] hover:bg-[#3a3a3a] rounded-lg p-4 transition-colors"
                >
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div>
                      <h3 className="font-bold text-lg mb-1">{tournament.name}</h3>
                      <div className="text-sm text-gray-400 mb-2">
                        <span className="inline-block bg-gray-600 text-white text-xs px-2 py-0.5 rounded mr-2">Completed</span>
                        <span className="inline-block bg-[#ff7f32] text-white text-xs px-2 py-0.5 rounded mr-2">
                          {tournament.type?.replace(/\b\w/g, (l: string) => l.toUpperCase()) || "-"}
                        </span>
                        <span>{tournament.participants?.length || 0} Participants</span>
                      </div>
                      <div className="text-sm text-gray-400">
                        Game: {tournament.game || "-"} • 
                        Completed: {tournament.endDate ? formatDistanceToNow(new Date(tournament.endDate), { addSuffix: true }) : "-"}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-sm text-gray-400">
                        Organized by <span className="text-white">{tournament.organizer?.name || "-"}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

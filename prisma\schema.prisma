// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  name          String?
  fullName      String?
  phone         String?
  bio           String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  password      String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts      Account[]
  sessions      Session[]
  tournaments   Tournament[] @relation("TournamentOrganizer")
  participations TournamentParticipant[]
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Tournament {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  game        String
  startDate   DateTime
  endDate     DateTime
  status      String   @default("upcoming") // upcoming, ongoing, completed, cancelled, finalstage
  type        String   // single elimination, double elimination, round robin
  maxTeams    Int?

  // Tournament Configuration
  typeOfStage       String?  // "single" or "two"
  groupStageFormat  String?  // "round", "single", "double"
  groupAdvance      Int?     // number of participants advancing from each group
  finalStageFormat  String?  // "single", "double", "round"
  matchFormat       String?  // "best_of_1", "best_of_3", "best_of_5"

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  organizerId String   @db.ObjectId
  organizer   User     @relation("TournamentOrganizer", fields: [organizerId], references: [id])
  participants TournamentParticipant[]
  matches     Match[]
}

model TournamentParticipant {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  tournamentId String   @db.ObjectId
  userId       String   @db.ObjectId
  teamName     String?
  status       String   @default("registered") // registered, confirmed, eliminated, winner
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  tournament   Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  matchTeams   MatchTeam[]
  wonMatches   Match[]    @relation("MatchWinner")

  @@unique([tournamentId, userId])
}

model Match {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  tournamentId String   @db.ObjectId
  round        Int
  matchNumber  Int
  status       String   @default("scheduled") // scheduled, in_progress, completed
  winnerId     String?  @db.ObjectId
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  tournament   Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  teams        MatchTeam[]
  winner       TournamentParticipant?  @relation("MatchWinner", fields: [winnerId], references: [id])

  @@unique([tournamentId, round, matchNumber])
}

model MatchTeam {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  matchId      String   @db.ObjectId
  participantId String  @db.ObjectId
  createdAt    DateTime @default(now())

  match        Match    @relation(fields: [matchId], references: [id], onDelete: Cascade)
  participant  TournamentParticipant @relation(fields: [participantId], references: [id], onDelete: Cascade)

  @@unique([matchId, participantId])
}

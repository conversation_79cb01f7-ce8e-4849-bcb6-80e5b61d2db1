"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import SidebarUser from "@/components/SidebarUser";
import LogoutButton from "@/components/LogoutButton";
import {
  LayoutDashboard,
  Plus,
  Trophy,
  User,
  Home,
  Settings,
  FileText,
  Calendar,
  Target,
  GitBranch
} from "lucide-react";

interface MenuItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
  badge?: number;
  isNew?: boolean;
}

const menuItems: MenuItem[] = [
  {
    href: "/user-dashboard",
    label: "Dashboard",
    icon: LayoutDashboard,
    description: "Overview & Statistics"
  },
  {
    href: "/create-tournament",
    label: "Create Tournament",
    icon: Plus,
    description: "Start New Tournament",
    isNew: true
  },
  {
    href: "/edit-profile",
    label: "Profile",
    icon: User,
    description: "Account Settings"
  }
];

interface DynamicSidebarProps {
  className?: string;
}

export default function DynamicSidebar({ className }: DynamicSidebarProps) {
  const pathname = usePathname();

  const isActiveRoute = (href: string): boolean => {
    if (href === "/user-dashboard") {
      return pathname === "/" || pathname === "/user-dashboard";
    }
    return pathname.startsWith(href);
  };

  // Add some interactive state
  const [hoveredItem, setHoveredItem] = React.useState<string | null>(null);
  const [currentTime, setCurrentTime] = React.useState(new Date());

  // Update time every minute
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  const getActiveStyles = (isActive: boolean) => {
    return cn(
      "group relative flex items-center gap-3 py-3 px-4 rounded-lg transition-all duration-300 ease-in-out",
      "hover:bg-[#333333] hover:shadow-md hover:scale-[1.02] hover:translate-x-1",
      "focus:outline-none focus:ring-2 focus:ring-[#ff7f32] focus:ring-opacity-50",
      "border border-transparent",
      isActive
        ? "bg-gradient-to-r from-[#ff7f32] to-[#e86a1f] text-white font-bold shadow-lg scale-[1.02] translate-x-1 border-orange-300/20"
        : "text-gray-300 hover:text-white hover:border-gray-600/30"
    );
  };

  const getIconStyles = (isActive: boolean) => {
    return cn(
      "w-5 h-5 transition-all duration-200",
      isActive
        ? "text-white drop-shadow-sm"
        : "text-gray-400 group-hover:text-[#ff7f32]"
    );
  };

  const getLabelStyles = (isActive: boolean) => {
    return cn(
      "flex-1 transition-all duration-200",
      isActive
        ? "text-white font-bold tracking-wide"
        : "text-gray-300 group-hover:text-white"
    );
  };

  return (
    <aside className={cn(
      "w-64 bg-[#232323] text-white flex flex-col py-8 px-4 border-r border-[#444444] min-h-screen sticky top-0",
      "shadow-xl",
      className
    )}>
      {/* User Section */}
      <div className="mb-8">
        <SidebarUser />
      </div>

      {/* Navigation Menu */}
      <nav className="flex flex-col gap-2 flex-1">
        <div className="mb-4">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4 mb-3">
            Navigation
          </h3>
          
          {menuItems.map((item) => {
            const isActive = isActiveRoute(item.href);
            const Icon = item.icon;
            
            return (
              <Link
                key={item.href}
                href={item.href}
                className={getActiveStyles(isActive)}
                onMouseEnter={() => setHoveredItem(item.href)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                {/* Active Indicator */}
                {isActive && (
                  <>
                    <div className="absolute left-0 top-0 bottom-0 w-1 bg-white rounded-r-full shadow-sm animate-pulse" />
                    <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-md animate-bounce" />
                  </>
                )}
                
                {/* Icon */}
                <Icon className={getIconStyles(isActive)} />
                
                {/* Label & Description */}
                <div className="flex-1 min-w-0">
                  <div className={getLabelStyles(isActive)}>
                    {item.label}
                  </div>
                  {item.description && (
                    <div className={cn(
                      "text-xs transition-all duration-200 truncate",
                      isActive
                        ? "text-orange-100"
                        : "text-gray-500 group-hover:text-gray-400"
                    )}>
                      {item.description}
                    </div>
                  )}
                </div>

                {/* Badges and Indicators */}
                <div className="flex items-center gap-2">
                  {/* New Badge */}
                  {item.isNew && !isActive && (
                    <span className="bg-green-500 text-white text-xs px-2 py-0.5 rounded-full font-bold animate-pulse">
                      NEW
                    </span>
                  )}

                  {/* Count Badge */}
                  {item.badge && item.badge > 0 && (
                    <span className="bg-red-500 text-white text-xs w-5 h-5 rounded-full flex items-center justify-center font-bold">
                      {item.badge > 99 ? '99+' : item.badge}
                    </span>
                  )}

                  {/* Active Badge */}
                  {isActive && (
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-white rounded-full shadow-sm animate-pulse" />
                      <span className="text-xs font-bold text-white/80 uppercase tracking-wider">ACTIVE</span>
                    </div>
                  )}
                </div>

                {/* Hover Tooltip */}
                {hoveredItem === item.href && !isActive && (
                  <div className="absolute left-full ml-2 top-1/2 transform -translate-y-1/2 z-50">
                    <div className="bg-[#1a1a1a] text-white px-3 py-2 rounded-lg shadow-xl border border-gray-600 whitespace-nowrap">
                      <div className="font-semibold">{item.label}</div>
                      {item.description && (
                        <div className="text-xs text-gray-400 mt-1">{item.description}</div>
                      )}
                      {/* Arrow */}
                      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-[#1a1a1a] border-l border-b border-gray-600 rotate-45"></div>
                    </div>
                  </div>
                )}
              </Link>
            );
          })}
        </div>

        {/* Quick Actions Section */}
        <div className="mt-6 pt-6 border-t border-[#444444]">
          <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4 mb-3">
            Quick Actions
          </h3>
          
          <div className="space-y-2">
            <Link
              href="/user-dashboard?tab=ongoing"
              className={cn(
                "flex items-center gap-3 py-2 px-4 rounded-lg transition-all duration-200",
                "text-gray-400 hover:text-[#ff7f32] hover:bg-[#333333]",
                "text-sm"
              )}
            >
              <Calendar className="w-4 h-4" />
              <span>Ongoing Tournaments</span>
            </Link>

            <Link
              href="/test-bracket"
              className={cn(
                "flex items-center gap-3 py-2 px-4 rounded-lg transition-all duration-200",
                "text-gray-400 hover:text-[#ff7f32] hover:bg-[#333333]",
                "text-sm"
              )}
            >
              <Target className="w-4 h-4" />
              <span>Test SE Bracket</span>
            </Link>

            <Link
              href="/test-de-bracket"
              className={cn(
                "flex items-center gap-3 py-2 px-4 rounded-lg transition-all duration-200",
                "text-gray-400 hover:text-[#ff7f32] hover:bg-[#333333]",
                "text-sm"
              )}
            >
              <GitBranch className="w-4 h-4" />
              <span>Test DE Bracket</span>
            </Link>
          </div>
        </div>

        {/* Status Indicator & Time */}
        <div className="mt-auto mb-6 space-y-3">
          {/* Current Time */}
          <div className="bg-[#1a1a1a] rounded-lg p-3 border border-[#444444]">
            <div className="text-center">
              <div className="text-lg font-bold text-[#ff7f32] mb-1">
                {currentTime.toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit',
                  hour12: false
                })}
              </div>
              <div className="text-xs text-gray-400">
                {currentTime.toLocaleDateString('en-US', {
                  weekday: 'short',
                  month: 'short',
                  day: 'numeric'
                })}
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="bg-[#333333] rounded-lg p-3 border border-[#444444]">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <span className="text-xs font-semibold text-green-400">SYSTEM ONLINE</span>
            </div>
          </div>
        </div>

        {/* Logout Section */}
        <div className="pt-4 border-t border-[#444444]">
          <LogoutButton />
        </div>
      </nav>
    </aside>
  );
}

// Hook untuk mendapatkan informasi menu aktif
export function useActiveMenu() {
  const pathname = usePathname();
  
  const activeMenuItem = menuItems.find(item => {
    if (item.href === "/user-dashboard") {
      return pathname === "/" || pathname === "/user-dashboard";
    }
    return pathname.startsWith(item.href);
  });

  return {
    activeMenuItem,
    isActive: (href: string) => {
      if (href === "/user-dashboard") {
        return pathname === "/" || pathname === "/user-dashboard";
      }
      return pathname.startsWith(href);
    }
  };
}

// Komponen untuk breadcrumb berdasarkan menu aktif
export function DynamicBreadcrumb() {
  const { activeMenuItem } = useActiveMenu();
  const pathname = usePathname();

  if (!activeMenuItem) return null;

  const breadcrumbItems = [
    { label: "Home", href: "/" },
    { label: activeMenuItem.label, href: activeMenuItem.href }
  ];

  // Add specific page if not on main menu page
  if (pathname !== activeMenuItem.href) {
    const pathSegments = pathname.split('/').filter(Boolean);
    const lastSegment = pathSegments[pathSegments.length - 1];
    
    if (lastSegment) {
      breadcrumbItems.push({
        label: lastSegment.charAt(0).toUpperCase() + lastSegment.slice(1).replace(/-/g, ' '),
        href: pathname
      });
    }
  }

  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-4">
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={item.href}>
          {index > 0 && <span className="text-gray-400">/</span>}
          <Link
            href={item.href}
            className={cn(
              "hover:text-[#ff7f32] transition-colors",
              index === breadcrumbItems.length - 1
                ? "text-[#ff7f32] font-semibold"
                : "text-gray-600"
            )}
          >
            {item.label}
          </Link>
        </React.Fragment>
      ))}
    </nav>
  );
}
